/**
 * Settings component for managing application settings
 */
(function() {
    'use strict';

    // DOM elements and global variables
    let settingsForm;
    let settingsContent;
    let settingsSearch;
    let settingsTabs;
    let settingsAlert;
    let resetButton;
    let rawConfigToggle;
    let rawConfigSection;
    let rawConfigEditor;
    let originalSettings = {};
    let allSettings = [];
    let activeTab = 'all';
    let saveTimer = null;
    let pendingSaves = new Set();

    // Model and search engine dropdown variables
    let modelOptions = [];
    let searchEngineOptions = [];

    // Store save timers for each setting key
    let saveTimers = {};
    let pendingSaveData = {};

    // Cache keys - same as research.js for shared caching
    const CACHE_KEYS = {
        MODELS: 'deepResearch.availableModels',
        SEARCH_ENGINES: 'deepResearch.searchEngines',
        CACHE_TIMESTAMP: 'deepResearch.cacheTimestamp'
    };

    // Cache expiration time (24 hours in milliseconds)
    const CACHE_EXPIRATION = 24 * 60 * 60 * 1000;

    /**
     * Helper function to generate custom dropdown HTML (similar to Jinja macro)
     * @param {object} params - Parameters for the dropdown
     * @returns {string} HTML string for the custom dropdown input part
     */
    function renderCustomDropdownHTML(params) {
        // Basic structure with input and list container
        let dropdownHTML = `
            <div class="custom-dropdown" id="${params.dropdown_id}">
                <input type="text"
                       id="${params.input_id}"
                       data-key="${params.data_setting_key || params.input_id}"
                       class="custom-dropdown-input"
                       placeholder="${params.placeholder}"
                       autocomplete="off"
                       aria-haspopup="listbox"
                       ${params.disabled ? "disabled" : ""}>
                <!-- Hidden input that will be included in form submission -->
                <input type="hidden" name="${params.input_id}" id="${params.input_id}_hidden" value="">
                <div class="custom-dropdown-list" id="${params.dropdown_id}-list"></div>
            </div>
        `;

        // Add refresh button if needed
        const refreshButtonHTML = params.show_refresh ? `
            <button type="button"
                    class="custom-dropdown-refresh-btn dropdown-refresh-button"
                    id="${params.input_id}-refresh"
                    aria-label="${params.refresh_aria_label || 'Refresh options'}">
                <i class="fas fa-sync-alt"></i>
            </button>
        ` : '';

        // Wrap with refresh container if needed
        if (params.show_refresh) {
            dropdownHTML = `
                <div class="custom-dropdown-with-refresh">
                    ${dropdownHTML} ${refreshButtonHTML}
                </div>
            `;
        }

        // Note: This returns only the input element part. Label and help text are handled outside.
        return dropdownHTML;
    }

    /**
     * Set up refresh buttons for model and search engine dropdowns
     */
    function setupRefreshButtons() {
        console.log('Setting up refresh buttons...');

        // Handle model refresh button
        const modelRefreshBtn = document.getElementById('llm.model-refresh');
        if (modelRefreshBtn) {
            console.log('Found and set up model refresh button:', modelRefreshBtn.id);
            modelRefreshBtn.addEventListener('click', function() {
                const icon = modelRefreshBtn.querySelector('i');
                if (icon) icon.className = 'fas fa-spinner fa-spin';
                modelRefreshBtn.classList.add('loading');

                // Reset the initialization flag to allow reinitializing the dropdown
                window.modelDropdownsInitialized = false;

                // Force refresh models and reinitialize
                fetchModelProviders(true)
                    .then(() => {
                        if (icon) icon.className = 'fas fa-sync-alt';
                        modelRefreshBtn.classList.remove('loading');

                        // Re-initialize model dropdowns with the new data
                        initializeModelDropdowns();

                        // Show success message
                        showAlert('Model list refreshed', 'success');
                    })
                    .catch(error => {
                        console.error('Error refreshing models:', error);
                        if (icon) icon.className = 'fas fa-sync-alt';
                        modelRefreshBtn.classList.remove('loading');
                        showAlert('Failed to refresh models', 'error');
                    });
            });
        } else {
            console.log('Could not find model refresh button');
        }

        // Handle search engine refresh button
        const searchEngineRefreshBtn = document.getElementById('search.tool-refresh');
        if (searchEngineRefreshBtn) {
            console.log('Found and set up search engine refresh button:', searchEngineRefreshBtn.id);
            searchEngineRefreshBtn.addEventListener('click', function() {
                const icon = searchEngineRefreshBtn.querySelector('i');
                if (icon) icon.className = 'fas fa-spinner fa-spin';
                searchEngineRefreshBtn.classList.add('loading');

                // Reset the initialization flag to allow reinitializing the dropdown
                window.searchEngineDropdownInitialized = false;

                // Force refresh search engines and reinitialize
                fetchSearchEngines(true)
                    .then(() => {
                        if (icon) icon.className = 'fas fa-sync-alt';
                        searchEngineRefreshBtn.classList.remove('loading');

                        // Re-initialize search engine dropdowns with the new data
                        initializeSearchEngineDropdowns();

                        // Show success message
                        showAlert('Search engine list refreshed', 'success');
                    })
                    .catch(error => {
                        console.error('Error refreshing search engines:', error);
                        if (icon) icon.className = 'fas fa-sync-alt';
                        searchEngineRefreshBtn.classList.remove('loading');
                        showAlert('Failed to refresh search engines', 'error');
                    });
            });
        } else {
            console.log('Could not find search engine refresh button');

            // Try to create refresh button if it doesn't exist for search engine
            createRefreshButton('search.tool', fetchSearchEngines);
        }
    }

    /**
     * Cache data in localStorage with timestamp
     * @param {string} key - The cache key
     * @param {Object} data - The data to cache
     */
    function cacheData(key, data) {
        try {
            // Store the data
            localStorage.setItem(key, JSON.stringify(data));

            // Update or set the timestamp
            let timestamps;
            try {
                timestamps = JSON.parse(localStorage.getItem(CACHE_KEYS.CACHE_TIMESTAMP) || '{}');
                // Ensure timestamps is an object, not a number or other type
                if (typeof timestamps !== 'object' || timestamps === null) {
                    timestamps = {};
                }
            } catch (e) {
                // If parsing fails, start with a new object
                timestamps = {};
            }

            timestamps[key] = Date.now();
            localStorage.setItem(CACHE_KEYS.CACHE_TIMESTAMP, JSON.stringify(timestamps));

            console.log(`Cached data for ${key}`);
        } catch (error) {
            console.error('Error caching data:', error);
        }
    }

    /**
     * Get cached data if it exists and is not expired
     * @param {string} key - The cache key
     * @returns {Object|null} The cached data or null if not found or expired
     */
    function getCachedData(key) {
        try {
            // Get timestamps
            let timestamps;
            try {
                timestamps = JSON.parse(localStorage.getItem(CACHE_KEYS.CACHE_TIMESTAMP) || '{}');
                // Ensure timestamps is an object, not a number or other type
                if (typeof timestamps !== 'object' || timestamps === null) {
                    timestamps = {};
                }
            } catch (e) {
                // If parsing fails, start with an empty object
                timestamps = {};
            }

            const timestamp = timestamps[key];

            // Check if data exists and is not expired
            if (timestamp && (Date.now() - timestamp < CACHE_EXPIRATION)) {
                try {
                const data = JSON.parse(localStorage.getItem(key));
                return data;
                } catch (e) {
                    console.error('Error parsing cached data:', e);
                    return null;
                }
            }
        } catch (error) {
            console.error('Error getting cached data:', error);
        }
        return null;
    }

    /**
     * Initialize auto-save handlers for settings inputs
     */
    function initAutoSaveHandlers() {
        // Only run this for the main settings dashboard
        if (!settingsContent) return;

        // Get all inputs in settings form
        const inputs = settingsForm.querySelectorAll('input, textarea, select');

        // Set up event handlers for each input
        inputs.forEach(input => {
            // Skip if this is a button or submit input
            if (input.type === 'button' || input.type === 'submit') return;

            // Set data-key attribute from name if not already set
            if (!input.getAttribute('data-key') && input.getAttribute('name')) {
                input.setAttribute('data-key', input.getAttribute('name'));
            }

            // Remove existing listeners to avoid duplicates
            input.removeEventListener('input', handleInputChange);
            input.removeEventListener('keydown', handleInputChange);
            input.removeEventListener('blur', handleInputChange);
            input.removeEventListener('change', handleInputChange);

            // For checkboxes, we use change event
            if (input.type === 'checkbox') {
                input.addEventListener('change', function(e) {
                    // For checkboxes, pass custom event type parameter to avoid issues
                    handleInputChange(e, 'change');
                });
            }
            // For selects, we use change event
            else if (input.tagName.toLowerCase() === 'select') {
                input.addEventListener('change', function(e) {
                    // Create a custom parameter instead of modifying e.type
                    handleInputChange(e, 'change');
                });

                input.addEventListener('blur', function(e) {
                    // Create a custom parameter instead of modifying e.type
                    handleInputChange(e, 'blur');
                });
            }
            // For text, number, etc. we monitor for changes but only save
            // on blur or Enter. We don't do anything with custom drop-downs
            // (we use the hidden input instead).
            else if (!input.classList.contains("custom-dropdown-input")) {
                // Listen for input events to track changes and validate in real-time
                input.addEventListener('input', function(e) {
                    // Create a custom parameter instead of modifying e.type
                    handleInputChange(e, 'input');
                });

                // Handle Enter key press for immediate saving
                input.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter') {
                        // Create a custom parameter instead of modifying e.type
                        handleInputChange(e, 'keydown');
                    }
                });

                // Save on blur if changes were made.
                if (input.id.endsWith("_hidden")) {
                    //  We can't use this for custom dropdowns, because it
                    //  will fire before the value has been changed, causing
                    //  it to read the wrong value.
                    input.addEventListener('change', function(e) {
                        // Create a custom parameter instead of modifying e.type
                        handleInputChange(e, 'change');
                    });
                } else {
                    input.addEventListener('blur', function(e) {
                        // Create a custom parameter instead of modifying e.type
                        handleInputChange(e, 'blur');
                    });
                }
            }
        });

        // Set up special handlers for JSON property controls
        const jsonPropertyControls = settingsForm.querySelectorAll('.json-property-control');

        jsonPropertyControls.forEach(control => {
            // Remove existing listeners
            control.removeEventListener('change', updateJsonFromControls);
            control.removeEventListener('input', updateJsonFromControls);
            control.removeEventListener('keydown', updateJsonFromControls);
            control.removeEventListener('blur', updateJsonFromControls);

            if (control.type === 'checkbox') {
                control.addEventListener('change', function(e) {
                    updateJsonFromControls(control, true); // true = force save
                });
            } else {
                control.addEventListener('input', function(e) {
                    updateJsonFromControls(control, false); // false = don't save yet
                });

                // Handle Enter key for JSON property controls
                control.addEventListener('keydown', function(e) {
                    if (e.key === 'Enter' && !e.shiftKey) {
                        e.preventDefault();
                        updateJsonFromControls(control, true); // true = force save
                        control.blur();
                    }
                });

                control.addEventListener('blur', function(e) {
                    updateJsonFromControls(control, true); // true = force save
                });
            }
        });

        // If the raw JSON editor is visible, set up its event handlers
        if (rawConfigEditor) {
            rawConfigEditor.addEventListener('input', handleRawJsonInput);
            rawConfigEditor.addEventListener('blur', function(e) {
                if (rawConfigEditor.getAttribute('data-modified') === 'true') {
                    handleRawJsonInput(e, true); // Force save on blur
                }
            });
        }
    }

    /**
     * Handle input change for autosave
     * @param {Event} e - The input change event
     * @param {string} [customEventType] - Optional event type parameter
     */
    function handleInputChange(e, customEventType) {
        // --- MODIFICATION START: Simplified handleInputChange ---
        const input = e.target;
        const eventType = customEventType || e.type;
        const key = input.dataset.key || input.name; // Get key using data-key first

        if (!key || input.disabled) return;

        let value;
        let shouldSaveImmediately = false;

        // Handle hidden inputs for custom dropdowns
        if (input.type === 'hidden' && input.id.endsWith('_hidden')) {
            value = input.value;
            shouldSaveImmediately = true; // Save immediately on hidden input change
            console.log(`[Hidden Input Change] Key: ${key}, Value: ${value}`);
        }
        // Handle checkboxes
        else if (input.type === 'checkbox') {
            value = input.checked;
            shouldSaveImmediately = true; // Checkboxes save immediately
        }
        // Handle standard selects
        else if (input.tagName.toLowerCase() === 'select') {
            value = input.value;
            // Save on change or blur if changed
            if (eventType === 'change' || eventType === 'blur') {
                shouldSaveImmediately = true;
            }
        }
         // Handle range/slider (save on change/input or blur)
         else if (input.type === 'range') {
            value = input.value;
            if (eventType === 'change' || eventType === 'input' || eventType === 'blur') {
                shouldSaveImmediately = true;
            }
         }
        // Handle other inputs (text, number, textarea) - Save on Enter or Blur
        else {
            value = input.value;
            // Basic validation for number
            if (input.type === 'number') {
               try {
                   const numValue = parseFloat(value);
                   const min = input.min ? parseFloat(input.min) : null;
                   const max = input.max ? parseFloat(input.max) : null;
                   if ((min !== null && numValue < min) || (max !== null && numValue > max)) {
                        markInvalidInput(input, `Value must be between ${min ?? '-∞'} and ${max ?? '∞'}`);
                        return; // Don't save invalid number
                   }
                   value = numValue; // Use parsed number
               } catch {
                    markInvalidInput(input, 'Invalid number');
                    return;
               }
            }
            // Save on Enter or Blur
            if ((eventType === 'keydown' && e.key === 'Enter' && !e.shiftKey) || eventType === 'blur') {
                shouldSaveImmediately = true;
                 if (eventType === 'keydown') e.preventDefault(); // Prevent form submission on enter
            }
        }

        // Clear previous errors
        markInvalidInput(input, null);

        // Compare with original value
        const originalValue = originalSettings.hasOwnProperty(key) ? originalSettings[key] : undefined;
        const hasChanged = !areValuesEqual(value, originalValue);

        console.log(`[Input Change] Key: ${key}, Value: ${value}, Event: ${eventType}, Changed: ${hasChanged}, Save Immediately: ${shouldSaveImmediately}`);

        if (hasChanged) {
            // Mark parent item as modified
            const item = input.closest('.settings-item');
            if (item) item.classList.add('settings-modified');

            // Save if needed
            if (shouldSaveImmediately) {
                const formData = { [key]: value };
                submitSettingsData(formData, input); // Direct submit might be better than debouncing here

                // If saved on Enter, blur the input
                if (eventType === 'keydown' && e.key === 'Enter') {
                    input.blur();
                }
            }
        } else {
            // If blur event and no changes, remove modified indicator maybe?
            if (eventType === 'blur') {
                const item = input.closest('.settings-item');
                if (item) item.classList.remove('settings-modified');
            }
        }
        // --- MODIFICATION END ---
    }

    /**
     * Compare two values for equality, handling different types
     * @param {any} value1 - First value
     * @param {any} value2 - Second value
     * @returns {boolean} - Whether the values are equal
     */
    function areValuesEqual(value1, value2) {
        // Handle null/undefined
        if (value1 === null && value2 === null) return true;
        if (value1 === undefined && value2 === undefined) return true;
        if (value1 === null && value2 === undefined) return true;
        if (value1 === undefined && value2 === null) return true;

        // If one is null/undefined but the other isn't
        if ((value1 === null || value1 === undefined) && (value2 !== null && value2 !== undefined)) return false;
        if ((value2 === null || value2 === undefined) && (value1 !== null && value1 !== undefined)) return false;

        // Handle different types
        const type1 = typeof value1;
        const type2 = typeof value2;

        // If types are different, they're not equal
        // Except for numbers and strings that might be equivalent
        if (type1 !== type2) {
            // Special case for numeric strings vs numbers
            if ((type1 === 'number' && type2 === 'string') || (type1 === 'string' && type2 === 'number')) {
                return String(value1) === String(value2);
            }
            return false;
        }

        // Handle objects (including arrays)
        if (type1 === 'object') {
            // Handle arrays
            if (Array.isArray(value1) && Array.isArray(value2)) {
                if (value1.length !== value2.length) return false;
                return JSON.stringify(value1) === JSON.stringify(value2);
            }

            // Handle objects
            return JSON.stringify(value1) === JSON.stringify(value2);
        }

        // Handle primitives
        return value1 === value2;
    }

    /**
     * Handle input to raw JSON fields for validation
     * @param {Event} e - The input event
     */
    function handleRawJsonInput(e) {
        const input = e.target;

        try {
            // Try to parse the JSON
            JSON.parse(input.value);

            // Valid JSON, remove any error styling
            const settingsItem = input.closest('.settings-item');
            if (settingsItem) {
                settingsItem.classList.remove('settings-error');

                // Remove any error message
                const errorMsg = settingsItem.querySelector('.settings-error-message');
                if (errorMsg) {
                    errorMsg.remove();
                }
            }
            input.classList.remove('settings-error');
        } catch (e) {
            // Invalid JSON, mark as error but don't prevent typing
            input.classList.add('settings-error');

            // Don't show error message while actively typing, only on blur
            input.addEventListener('blur', function onBlur() {
                try {
                    JSON.parse(input.value);
                    // Valid JSON on blur, clear any error
                    markInvalidInput(input, null);
                } catch (e) {
                    // Still invalid on blur, show error
                    markInvalidInput(input, 'Invalid JSON format: ' + e.message);
                }
                // Remove this blur handler after it runs once
                input.removeEventListener('blur', onBlur);
            }, { once: true });
        }
    }

    /**
     * Mark an input as invalid with error styling
     * @param {HTMLElement} input - The input element
     * @param {string|null} errorMessage - The error message or null to clear error
     */
    function markInvalidInput(input, errorMessage) {
        const settingsItem = input.closest('.settings-item');
        if (!settingsItem) return;

        // Clear existing error message
        const existingMsg = settingsItem.querySelector('.settings-error-message');
        if (existingMsg) {
            existingMsg.remove();
        }

        if (errorMessage) {
            // Add error class
            settingsItem.classList.add('settings-error');
            input.classList.add('settings-error');

            // Create error message
            const errorMsg = document.createElement('div');
            errorMsg.className = 'settings-error-message';
            errorMsg.textContent = errorMessage;
            settingsItem.appendChild(errorMsg);
        } else {
            // Remove error class
            settingsItem.classList.remove('settings-error');
            input.classList.remove('settings-error');
        }
    }

    /**
     * Schedule a debounced save operation
     * @param {Object} formData - The form data to save
     * @param {HTMLElement} sourceElement - The element that triggered the save
     */
    function scheduleSave(formData, sourceElement) {
        // Merge the form data with any existing pending save data
        Object.entries(formData).forEach(([key, value]) => {
            pendingSaveData[key] = value;

            // Clear any existing timer for this specific key
            if (saveTimers[key]) {
                clearTimeout(saveTimers[key]);
            }

            // Set loading state on the source element
            if (sourceElement) {
                sourceElement.classList.add('saving');
            }

            // Create a new timer for this specific key
            saveTimers[key] = setTimeout(() => {
                // Create a single-key form data object with just this setting
                const singleSettingData = { [key]: pendingSaveData[key] };

                // Submit just this setting's data
                submitSettingsData(singleSettingData, sourceElement);

                // Clear this key from pending saves
                delete pendingSaveData[key];
                delete saveTimers[key];
            }, 800); // 800ms debounce
        });
    }

    /**
     * Initialize expanded JSON controls
     * This sets up event listeners for the individual form controls that represent JSON properties
     */
    function initExpandedJsonControls() {
        // Find all JSON property controls
        document.querySelectorAll('.json-property-control').forEach(control => {
            // When the control changes, update the hidden JSON field
            control.addEventListener('change', function() {
                updateJsonFromControls(this);
            });

            // For text and number inputs, also listen for input events
            if (control.tagName === 'INPUT' && (control.type === 'text' || control.type === 'number')) {
                control.addEventListener('input', function() {
                    updateJsonFromControls(this);
                });
            }
        });
    }

    /**
     * Update JSON data from individual controls
     * @param {HTMLElement} changedControl - The control that triggered the update
     * @param {boolean} forceSave - Whether to force an update to the server
     */
    function updateJsonFromControls(changedControl, forceSave = false) {
        const parentKey = changedControl.dataset.parentKey;
        const property = changedControl.dataset.property;

        if (!parentKey || !property) return;

        // Find all controls for this parent JSON
        const controls = document.querySelectorAll(`.json-property-control[data-parent-key="${parentKey}"]`);

        // Create an object to hold the JSON data
        const jsonData = {};

        // Populate the object with values from all controls
        controls.forEach(control => {
            const prop = control.dataset.property;
            let value = null;

            if (control.type === 'checkbox') {
                value = control.checked;
            } else if (control.type === 'number') {
                value = parseFloat(control.value);
            } else if (control.tagName === 'SELECT') {
                value = control.value;
            } else {
                value = control.value;
                // Try to convert to number if it's numeric
                if (!isNaN(value) && value !== '') {
                    value = parseFloat(value);
                }
            }

            jsonData[prop] = value;
        });

        // Find the hidden input that stores the original JSON
        const originalInput = document.getElementById(`${parentKey.replace(/\./g, '-')}_original`);
        let originalJson = {};

        if (originalInput) {
            // Get the original JSON
            try {
                originalJson = JSON.parse(originalInput.value);
            } catch (e) {
                console.error('Error parsing original JSON:', e);
                // Create an empty object if parsing fails
                originalJson = {};
            }
        }

        // Check if there's actually a change before saving
        const hasChanged = !areObjectsEqual(jsonData, originalJson);

        // Mark the parent container as modified if there's a change
        const settingItem = changedControl.closest('.settings-item');
        if (settingItem && hasChanged) {
            settingItem.classList.add('settings-modified');
        }

        // Update the UI even if we're not saving to the server
        if (originalInput) {
            // Update the original JSON with new values
            Object.assign(originalJson, jsonData);
            originalInput.value = JSON.stringify(originalJson);
        }

        // Also update any textarea that might display this JSON
        const jsonTextarea = document.getElementById(parentKey.replace(/\./g, '-'));
        if (jsonTextarea && jsonTextarea.tagName === 'TEXTAREA') {
            jsonTextarea.value = JSON.stringify(jsonData, null, 2);
        }

        // If we have a raw config editor, update it as well
        if (rawConfigEditor) {
            try {
                const rawConfig = JSON.parse(rawConfigEditor.value);
                const parts = parentKey.split('.');
                const prefix = parts[0]; // app, llm, search, etc.

                if (rawConfig[prefix]) {
                    const subKey = parentKey.substring(prefix.length + 1);
                    rawConfig[prefix][subKey] = jsonData;
                    rawConfigEditor.value = JSON.stringify(rawConfig, null, 2);
                }
            } catch (e) {
                console.log('Error updating raw config:', e);
            }
        }

        // Only save to the server if forced or there's a change
        if ((forceSave && hasChanged) || (changedControl.type === 'checkbox' && hasChanged)) {
            // Auto-save this setting
            const formData = {};
            formData[parentKey] = jsonData;
            submitSettingsData(formData, changedControl);
        }
    }

    /**
     * Compare two objects for equality
     * @param {Object} obj1 - First object
     * @param {Object} obj2 - Second object
     * @returns {boolean} - Whether the objects are equal
     */
    function areObjectsEqual(obj1, obj2) {
        // Get the keys of both objects
        const keys1 = Object.keys(obj1);
        const keys2 = Object.keys(obj2);

        // If the number of keys is different, they're not equal
        if (keys1.length !== keys2.length) return false;

        // Check each key/value pair
        for (const key of keys1) {
            // If the key doesn't exist in obj2, not equal
            if (!obj2.hasOwnProperty(key)) return false;

            // If the values are not equal, not equal
            if (!areValuesEqual(obj1[key], obj2[key])) return false;
        }

        // All keys/values match
        return true;
    }

    /**
     * Initialize specific settings page form handlers
     */
    function initSpecificSettingsForm() {
        // Get the form ID to determine which specific page we're on
        const specificForm = document.getElementById('report-settings-form') ||
                             document.getElementById('llm-settings-form') ||
                             document.getElementById('search-settings-form') ||
                             document.getElementById('app-settings-form');

        if (specificForm) {
            // Add form submission handler
            specificForm.addEventListener('submit', function(e) {
                // Handle checkbox values
                const checkboxes = specificForm.querySelectorAll('input[type="checkbox"]');
                checkboxes.forEach(checkbox => {
                    if (!checkbox.checked) {
                        // Create a hidden input for unchecked boxes
                        const hidden = document.createElement('input');
                        hidden.type = 'hidden';
                        hidden.name = checkbox.name;
                        hidden.value = 'false';
                        specificForm.appendChild(hidden);
                    }
                });

                // Check for validation errors in JSON textareas
                let hasInvalidJson = false;

                document.querySelectorAll('.json-content').forEach(textarea => {
                    try {
                        // Try to parse JSON to validate
                        JSON.parse(textarea.value);
                    } catch (e) {
                        // If it's not valid JSON, show an error
                        e.preventDefault();
                        hasInvalidJson = true;

                        // Find the closest settings-item
                        const settingsItem = textarea.closest('.settings-item');
                        if (settingsItem) {
                            settingsItem.classList.add('settings-error');

                            // Add error message if it doesn't exist
                            let errorMsg = settingsItem.querySelector('.settings-error-message');
                            if (!errorMsg) {
                                errorMsg = document.createElement('div');
                                errorMsg.className = 'settings-error-message';
                                settingsItem.appendChild(errorMsg);
                            }
                            errorMsg.textContent = 'Invalid JSON format';
                        }
                    }
                });

                // Handle JSON from expanded controls
                document.querySelectorAll('input[id$="_original"]').forEach(input => {
                    if (input.name.endsWith('_original')) {
                        const actualName = input.name.replace('_original', '');

                        // Create a hidden input with the actual name
                        const hiddenInput = document.createElement('input');
                        hiddenInput.type = 'hidden';
                        hiddenInput.name = actualName;
                        hiddenInput.value = input.value;
                        specificForm.appendChild(hiddenInput);
                    }
                });

                if (hasInvalidJson) {
                    e.preventDefault();
                    return false;
                }
            });
        }
    }

    /**
     * Initialize range inputs to display their values
     */
    function initRangeInputs() {
        const rangeInputs = document.querySelectorAll('input[type="range"]');

        rangeInputs.forEach(range => {
            const valueDisplay = document.getElementById(`${range.id}-value`) || range.nextElementSibling;

            if (valueDisplay &&
                (valueDisplay.classList.contains('settings-range-value') ||
                 valueDisplay.classList.contains('range-value'))) {
                // Set initial value
                valueDisplay.textContent = range.value;

                // Update on input change
                range.addEventListener('input', () => {
                    valueDisplay.textContent = range.value;
                });
            }
        });
    }

    /**
     * Initialize accordion behavior
     */
    function initAccordions() {
        document.querySelectorAll('.settings-section-header').forEach(header => {
            const targetId = header.dataset.target;
            const target = document.getElementById(targetId);

            if (target) {
                // Set initial state - expanded
                header.classList.remove('collapsed');
                target.style.display = 'block';

                header.addEventListener('click', () => {
                    header.classList.toggle('collapsed');
                    target.style.display = header.classList.contains('collapsed') ? 'none' : 'block';

                    // Rotate chevron icon
                    const icon = header.querySelector('.settings-toggle-icon i');
                    if (icon) {
                        icon.style.transform = header.classList.contains('collapsed') ? 'rotate(-90deg)' : '';
                    }
                });
            }
        });
    }

    /**
     * Format JSON in textareas
     */
    function initJsonFormatting() {
        document.querySelectorAll('.json-content').forEach(textarea => {
            const value = textarea.value.trim();

            if (value && (value.startsWith('{') || value.startsWith('['))) {
                try {
                    const formatted = JSON.stringify(JSON.parse(value), null, 2);
                    textarea.value = formatted;
                } catch (e) {
                    // Not valid JSON, leave as is
                    console.log('Error formatting JSON:', e);
                }
            }

            // Add event listener to format on input
            textarea.addEventListener('input', function() {
                if (this.value.trim() && (this.value.trim().startsWith('{') || this.value.trim().startsWith('['))) {
                    try {
                        const obj = JSON.parse(this.value);
                        const formatted = JSON.stringify(obj, null, 2);

                        // Only update if actually different (to avoid cursor jumping)
                        if (this.value !== formatted) {
                            // Remember cursor position
                            const selectionStart = this.selectionStart;
                            const selectionEnd = this.selectionEnd;

                            this.value = formatted;

                            // Try to restore cursor
                            this.setSelectionRange(selectionStart, selectionEnd);
                        }
                    } catch (e) {
                        // Invalid JSON, just leave it alone
                    }
                }
            });
        });

        // Convert text inputs with JSON content to textareas
        document.querySelectorAll('.settings-input').forEach(input => {
            const value = input.value.trim();

            // Skip if the value is "[object Object]" which isn't valid JSON
            if (value === "[object Object]") {
                // Replace with an empty object
                input.value = "{}";
                console.log('Fixed [object Object] string in input:', input.name);
                return;
            }

            if (value && (value.startsWith('{') || value.startsWith('['))) {
                try {
                    // Try to parse as JSON to validate
                    JSON.parse(value);

                    // Create a new textarea
                    const textarea = document.createElement('textarea');
                    textarea.id = input.id;
                    textarea.name = input.name;
                    textarea.className = 'settings-textarea json-content';
                    textarea.disabled = input.disabled;

                    try {
                        textarea.value = JSON.stringify(JSON.parse(value), null, 2);
                    } catch (e) {
                        textarea.value = value;
                    }

                    // Replace the input with textarea
                    input.parentNode.replaceChild(textarea, input);
                } catch (e) {
                    // Not valid JSON, leave as is
                    console.log('Error converting JSON input to textarea:', e);
                }
            }
        });
    }

    // Editorial Team View is the default mode
    const isEditorialTeam = true;

    /**
     * Load settings from the API
     */
    function loadSettings() {
        // Only run this for the main settings dashboard
        if (!settingsContent) return;

        fetch('/research/settings/api')
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    // Process settings to handle object values and check for corruption
                    allSettings = processSettings(data.settings);

                    // Filter settings for editorial team (default mode)
                    allSettings = filterSettingsForEditorialTeam(allSettings);

                    // Store original values
                    allSettings.forEach(setting => {
                        originalSettings[setting.key] = setting.value;
                    });

                    // Handle UI visibility for editorial team
                    handleEditorialTeamUI();

                    // Render settings by tab
                    renderSettingsByTab(activeTab);

                    // Initialize auto-save handlers
                    setTimeout(initAutoSaveHandlers, 300);

                    // Initialize the dropdowns after the settings are loaded
                    if (activeTab === 'llm' || activeTab === 'all') {
                        setTimeout(initializeModelDropdowns, 300);
                    }
                    if (activeTab === 'search' || activeTab === 'all') {
                        setTimeout(initializeSearchEngineDropdowns, 300);
                    }

                    // Prepare the raw JSON editor if it exists
                    prepareRawJsonEditor();

                    // Initialize expanded JSON controls
                    setTimeout(() => {
                        initExpandedJsonControls();
                    }, 100);

                    // Show the form actions after settings are loaded
                    showFormActions();
                } else {
                    showAlert('Error loading settings: ' + data.message, 'error');
                }
            })
            .catch(error => {
                showAlert('Error loading settings: ' + error, 'error');
            });
    }

    /**
     * Filter settings for editorial team view
     */
    function filterSettingsForEditorialTeam(settings) {
        // Settings that should be VISIBLE to Editorial Team
        const editorialVisibleSettings = new Set([
            // Application Sub-Tab - Show to Editorial Team
            'app.enable_notifications',
            'app.theme',
            'app.default_theme',
            'app.dark_mode',

            // Search Engine Sub-Tab - Show to Editorial Team
            'search.search_language',
            'search.time_period',
            'search.safe_search',
            'search.quality_check_urls',
            'search.region',

            // Language Models Sub-Tab - Show to Editorial Team
            'llm.temperature',  // Will be renamed to "creativity vs. determinism"
            'llm.max_tokens',   // Will be renamed to "output length"
        ]);

        return settings.filter(setting => {
            // Show all report settings (maintain all settings in reports sub-tab)
            if (setting.type === 'report' || setting.category === 'report' ||
                setting.category === 'report_parameters' || setting.key.startsWith('report.')) {
                return true;
            }

            // For other settings, only show those in the editorial visible list
            return editorialVisibleSettings.has(setting.key);
        });
    }

    /**
     * Handle UI visibility for editorial team
     */
    function handleEditorialTeamUI() {
        // Hide the JSON configuration toggle
        const rawConfigToggle = document.getElementById('toggle-raw-config');
        if (rawConfigToggle) {
            rawConfigToggle.style.display = 'none';
        }

        // Hide the form actions (Reset to Defaults and Save All Settings buttons)
        const formActions = document.querySelector('.form-actions');
        if (formActions) {
            formActions.style.display = 'none';
        }

        // Add editorial team indicator
        addEditorialTeamIndicator();

        // Update setting names for editorial team
        updateSettingNamesForEditorial();
    }

    /**
     * Show the form actions after settings are loaded
     */
    function showFormActions() {
        const formActions = document.querySelector('.form-actions');
        if (formActions) {
            // Only show if not in editorial team mode
            // Check if editorial team indicator exists to determine if we're in editorial mode
            const editorialIndicator = document.querySelector('.editorial-team-indicator');
            if (!editorialIndicator) {
                formActions.style.display = 'block';

                // Add the fix corrupted settings button if it doesn't exist
                const resetToDefaultsButton = document.getElementById('reset-to-defaults-button');
                const existingFixButton = document.getElementById('fix-corrupted-button');

                if (resetToDefaultsButton && !existingFixButton) {
                    const fixCorruptedButton = document.createElement('button');
                    fixCorruptedButton.setAttribute('type', 'button');
                    fixCorruptedButton.setAttribute('id', 'fix-corrupted-button');
                    fixCorruptedButton.className = 'btn btn-info';
                    fixCorruptedButton.innerHTML = '<i class="fas fa-wrench"></i> Fix Corrupted Settings';
                    fixCorruptedButton.addEventListener('click', handleFixCorruptedSettings);

                    // Insert it after the reset to defaults button
                    resetToDefaultsButton.insertAdjacentElement('afterend', fixCorruptedButton);
                }
            }
        }
    }

    /**
     * Add editorial team indicator to the page
     */
    function addEditorialTeamIndicator() {
        const pageHeader = document.querySelector('.page-header');
        if (pageHeader && !document.querySelector('.editorial-team-indicator')) {
            const indicator = document.createElement('div');
            indicator.className = 'editorial-team-indicator';
            indicator.innerHTML = `
                <div class="role-badge editorial-team">
                    <i class="fas fa-users"></i> Editorial Team View
                </div>
            `;
            pageHeader.appendChild(indicator);
        }
    }

    /**
     * Update setting names to be more user-friendly for editorial team
     */
    function updateSettingNamesForEditorial() {
        const nameUpdates = {
            'llm.temperature': 'Creativity vs. Determinism',
            'llm.max_tokens': 'Output Length (words)',
            'app.enable_notifications': 'Enable Notifications',
            'app.theme': 'UI Theme',
            'search.search_language': 'Search Language',
            'search.time_period': 'Time Period',
            'search.safe_search': 'Safe Search',
            'search.quality_check_urls': 'Quality Check URLs',
            'search.region': 'Search Region',
        };

        // Update the setting names in allSettings
        allSettings.forEach(setting => {
            if (nameUpdates[setting.key]) {
                setting.name = nameUpdates[setting.key];
            }
        });
    }

    /**
     * Format category names to be more user-friendly
     * @param {string} key - The setting key
     * @param {string} category - The category name
     * @returns {string} - The formatted category name
     */
    function formatCategoryName(key, category) {
        // Special cases for known categories
        if (category === 'app_interface') return 'App Interface';
        if (category === 'app_parameters') return 'App Parameters';
        if (category === 'llm_general') return 'LLM General';
        if (category === 'llm_parameters') return 'LLM Parameters';
        if (category === 'report_parameters') return 'Report Parameters';
        if (category === 'search_general') return 'Search General';
        if (category === 'search_parameters') return 'Search Parameters';

        // Remove any underscores and capitalize each word
        let formattedCategory = category.replace(/_/g, ' ');

        // Capitalize first letter of each word
        formattedCategory = formattedCategory.split(' ')
            .map(word => word.charAt(0).toUpperCase() + word.slice(1))
            .join(' ');

        return formattedCategory;
    }

    /**
     * Organize settings to avoid duplicate group names and improve organization
     * @param {Array} settings - The settings array
     * @param {string} tab - The current tab
     * @returns {Object} - The organized settings
     */
    function organizeSettings(settings, tab) {
        // Create a mapping of types
        const typeMap = {
            'app': 'Application',
            'llm': 'Language Models',
            'search': 'Search Engines',
            'report': 'Reports'
        };

        // Define settings that should only appear in specific tabs
        const tabSpecificSettings = {
            'llm': [
                'provider',
                'model',
                'temperature',
                'max_tokens',
                'lmstudio_url',
                'api_key'
            ],
            'search': [
                'iterations',
                'max_filtered_results',
                'max_results',
                'quality_check_urls',
                'questions_per_iteration',
                'region',
                'search_engine',
                'searches_per_section',
                'skip_relevance_filter',
                'safe_search',
                'search_language',
                'time_period',
                'tool',
                'snippets_only'
            ],
            'report': [
                'enable_fact_checking',
                'knowledge_accumulation',
                'knowledge_accumulation_context_limit',
                'output_dir',
                'detailed_citations'
            ],
            'app': [
                'debug',
                'host',
                'port',
                'enable_notifications',
                'web_interface',
                'enable_web',
                'dark_mode',
                'default_theme',
                'theme'
            ]
        };

        // Priority settings that should appear at the top of each tab
        const prioritySettings = {
            'app': ['enable_web', 'enable_notifications', 'web_interface', 'theme', 'default_theme', 'dark_mode', 'debug', 'host', 'port'],
            'llm': ['provider', 'model', 'temperature', 'max_tokens', 'api_key', 'lmstudio_url'],
            'search': ['tool', 'iterations', 'questions_per_iteration', 'max_results', 'region', 'search_engine'],
            'report': ['enable_fact_checking', 'knowledge_accumulation', 'output_dir', 'detailed_citations']
        };

        // Group by prefix and category
        const grouped = {};

        // Filter settings based on current tab
        const filteredSettings = settings.filter(setting => {
            const parts = setting.key.split('.');
            const prefix = parts[0]; // app, llm, search, etc.
            const subKey = parts[1]; // The actual key name without prefix

            // Filter out nested settings like app.llm, app.search, app.general, app.web, etc.
            if (prefix === 'app' && (subKey === 'llm' || subKey === 'search' || subKey === 'general' || subKey === 'web')) {
                return false;
            }

            // Filter out fact checking duplicates - only keep in report tab
            if (prefix !== 'report' && subKey === 'enable_fact_checking') {
                return false;
            }

            // Filter out knowledge_accumulation duplicates - only keep in report tab
            if (prefix !== 'report' && (subKey === 'knowledge_accumulation' || subKey === 'knowledge_accumulation_context_limit')) {
                return false;
            }

            // Filter out settings that are not marked as visible.
            if (!setting.visible) {
                return false;
            }

            // If we're on a specific tab, only show settings for that tab
            if (tab !== 'all') {
                // Only show settings in tab-specific lists for that tab
                if (tab === prefix) {
                    // For tab-specific settings, make sure they're in the list
                    if (tabSpecificSettings[tab] && tabSpecificSettings[tab].includes(subKey)) {
                        return true;
                    }
                    // For settings not in any tab-specific list, allow showing them in their own tab
                    for (const otherTab in tabSpecificSettings) {
                        if (otherTab !== tab && tabSpecificSettings[otherTab].includes(subKey)) {
                            return false;
                        }
                    }
                    return true;
                }
                return false;
            }

            // For "all" tab, filter out duplicates and specialized settings
            // Check if this setting belongs exclusively to a specific tab
            for (const tabName in tabSpecificSettings) {
                if (tabSpecificSettings[tabName].includes(subKey) && prefix !== tabName) {
                    // Don't show this setting if it belongs to a different tab
                    return false;
                }
            }

            // Include all remaining settings in the "all" tab
            return true;
        });

        // First pass: group settings by prefix and category
        filteredSettings.forEach(setting => {
            const parts = setting.key.split('.');
            const prefix = parts[0]; // app, llm, search, etc.
            const subKey = parts[1]; // The setting key without prefix

            // Create namespace if needed
            if (!grouped[prefix]) {
                grouped[prefix] = {};
            }

            // Use category or create one based on subkey
            let category = setting.category || 'general';

            // Format the category name to be user-friendly
            category = formatCategoryName(prefix, category);

            // For duplicate "general" categories, prefix with the type
            if (category.toLowerCase() === 'general') {
                category = `${typeMap[prefix] || prefix.charAt(0).toUpperCase() + prefix.slice(1)} General`;
            }

            // Create category array if needed
            if (!grouped[prefix][category]) {
                grouped[prefix][category] = [];
            }

            // Add setting to category
            grouped[prefix][category].push(setting);
        });

        // Second pass: sort settings within each category by priority and sort categories
        for (const prefix in grouped) {
            // Get existing categories for this prefix
            const categories = Object.keys(grouped[prefix]);

            // --- MODIFICATION START: Prioritize categories containing specific dropdowns ---
            // Identify high-priority categories
            const highPriorityCategories = [];
            const otherCategories = [];
            const priorityKeysForPrefix = prioritySettings[prefix] || [];
            const highestPriorityKeys = ['provider', 'model', 'tool']; // Keys whose *containing category* should be first

            categories.forEach(category => {
                const containsHighestPriority = grouped[prefix][category].some(setting => {
                    const subKey = setting.key.split('.')[1];
                    // Ensure the setting key itself is also in the general priority list for the prefix
                    return highestPriorityKeys.includes(subKey) && priorityKeysForPrefix.includes(subKey);
                });
                if (containsHighestPriority) {
                    highPriorityCategories.push(category);
                } else {
                    otherCategories.push(category);
                }
            });

            // Sort the high-priority categories (e.g., alphabetically or by specific order if needed)
            highPriorityCategories.sort((a, b) => {
                // Simple sort for now, could be more specific if needed
                // Example: ensure "Provider" comes before "Model" if both are high priority
                const order = ['Provider', 'Model', 'Tool'];
                const aIndex = order.findIndex(word => a.includes(word));
                const bIndex = order.findIndex(word => b.includes(word));
                if (aIndex !== -1 && bIndex !== -1) return aIndex - bIndex;
                if (aIndex !== -1) return -1;
                if (bIndex !== -1) return 1;
                return a.localeCompare(b);
            });

            // Sort other categories based on existing logic (e.g., using categoryOrder)
            const categoryOrder = ['General', 'Interface', 'Connection', 'API', 'Parameters']; // Adjusted order slightly
            otherCategories.sort((a, b) => {
                const aIndex = categoryOrder.findIndex(word => a.includes(word));
                const bIndex = categoryOrder.findIndex(word => b.includes(word));
                if (aIndex !== -1 && bIndex !== -1) return aIndex - bIndex;
                if (aIndex !== -1) return -1;
                if (bIndex !== -1) return 1;
                return a.localeCompare(b);
            });

            // Combine sorted categories
            const sortedCategoryNames = [...highPriorityCategories, ...otherCategories];

            // Create new object with sorted categories and sorted settings within each
            const sortedPrefixedCategories = {};
            sortedCategoryNames.forEach(category => {
                sortedPrefixedCategories[category] = grouped[prefix][category];

                // Sort settings within this category (existing logic seems okay)
                sortedPrefixedCategories[category].sort((a, b) => {
                    const aKey = a.key.split('.')[1];
                    const bKey = b.key.split('.')[1];
                    const priorities = prioritySettings[prefix] || [];
                    const aIndex = priorities.indexOf(aKey);
                    const bIndex = priorities.indexOf(bKey);
                    if (aIndex !== -1 && bIndex !== -1) return aIndex - bIndex;
                    if (aIndex !== -1) return -1;
                    if (bIndex !== -1) return 1;
                    return aKey.localeCompare(bKey);
                });
            });

            // Replace original categories with sorted ones
            grouped[prefix] = sortedPrefixedCategories;
            // --- MODIFICATION END ---
        }

        return grouped;
    }

    /**
     * Render settings based on active tab
     * @param {string} tab - The active tab
     */
    function renderSettingsByTab(tab) {
        // Only run this for the main settings dashboard
        if (!settingsContent) return;

        // Reset dropdown initialization state when switching tabs
        window.modelDropdownsInitialized = false;
        window.searchEngineDropdownInitialized = false;

        // Filter settings by tab
        let filteredSettings = allSettings;

        if (tab !== 'all') {
            filteredSettings = allSettings.filter(setting => setting.key.startsWith(tab + '.'));
        }

        // Organize settings to avoid duplicate groups
        const groupedSettings = organizeSettings(filteredSettings, tab);

        // Build HTML
        let html = '';

        // Define the order for the types in "all" tab
        const typeOrder = ['llm', 'search', 'report', 'app'];
        const prefixTypes = Object.keys(groupedSettings);

        // Sort prefixes by the defined order for the "all" tab
        if (tab === 'all') {
            prefixTypes.sort((a, b) => {
                const aIndex = typeOrder.indexOf(a);
                const bIndex = typeOrder.indexOf(b);

                // If both are in the ordered list, sort by that order
                if (aIndex !== -1 && bIndex !== -1) {
                    return aIndex - bIndex;
                }

                // If only one is in the list, it comes first
                if (aIndex !== -1) return -1;
                if (bIndex !== -1) return 1;

                // Alphabetically for anything else
                return a.localeCompare(b);
            });
        }

        // For each type (app, llm, search, etc.)
        for (const type of prefixTypes) {
            if (tab !== 'all' && type !== tab) continue;

            // For each category in this type
            for (const category in groupedSettings[type]) {
                const sectionId = `section-${type}-${category.replace(/\s+/g, '-').toLowerCase()}`;

                html += `
                <div class="settings-section">
                    <div class="settings-section-header" data-target="${sectionId}">
                        <div class="settings-section-title" title="${category}">
                            ${category}
                        </div>
                        <div class="settings-toggle-icon">
                            <i class="fas fa-chevron-down"></i>
                        </div>
                    </div>
                    <div id="${sectionId}" class="settings-section-body">
                `;

                // Add all settings in this category
                groupedSettings[type][category].forEach(setting => {
                    html += renderSettingItem(setting);
                });

                html += `
                    </div>
                </div>
                `;
            }
        }

        if (html === '') {
            html = '<div class="empty-state"><p>No settings found for this category</p></div>';
        }

        // Update the content
        settingsContent.innerHTML = html;

        // Check if the element exists immediately after setting innerHTML
        console.log('Checking for llm.model after render:', document.getElementById('llm.model'));

        // Initialize accordion behavior
        initAccordions();

        // Initialize JSON handling
        initJsonFormatting();

        // Initialize range inputs
        initRangeInputs();

        // Initialize expanded JSON controls
        setTimeout(() => {
            initExpandedJsonControls();
        }, 100);

        // Initialize dropdowns AFTER content is rendered
        initializeModelDropdowns();
        initializeSearchEngineDropdowns();
        // Also initialize the main setup which finds all dropdowns
        setupCustomDropdowns();
        // Setup provider change listener after rendering
        setupProviderChangeListener();
    }

    /**
     * Render a single setting item
     * @param {Object} setting - The setting object
     * @returns {string} - The HTML for the setting item
     */
    function renderSettingItem(setting) {
        // Log the setting being processed
        console.log('Processing Setting:', setting.key, 'UI Element:', setting.ui_element);

        const settingId = `setting-${setting.key.replace(/\./g, '-')}`;
        let inputElement = '';

        // Generate the appropriate input element based on UI element type
        switch(setting.ui_element) {
            case 'textarea':
                // Check if it's JSON
                let isJson = false;
                let jsonClass = '';

                if (typeof setting.value === 'string' &&
                    (setting.value.startsWith('{') || setting.value.startsWith('['))) {
                    isJson = true;
                    jsonClass = ' json-content';

                    // Try to format the JSON for better display
                    try {
                        setting.value = JSON.stringify(JSON.parse(setting.value), null, 2);
                    } catch (e) {
                        // If parsing fails, keep the original value
                        console.log('Error formatting JSON:', e);
                    }

                    // If it's an object (not an array), render individual controls
                    if (setting.value.startsWith('{')) {
                        try {
                            const jsonObj = JSON.parse(setting.value);
                            return renderExpandedJsonControls(setting, settingId, jsonObj);
                        } catch (e) {
                            console.log('Error parsing JSON for controls:', e);
                        }
                    }
                }

                inputElement = `
                    <textarea id="${settingId}" name="${setting.key}"
                        class="settings-textarea${jsonClass}"
                        ${!setting.editable ? 'disabled' : ''}
                    >${setting.value !== null ? setting.value : ''}</textarea>
                `;
                break;

            case 'select':
                // Handle specific keys that should use custom dropdowns
                if (setting.key === 'llm.provider') {
                    const dropdownParams = {
                        input_id: setting.key,
                        dropdown_id: settingId + "-dropdown",
                        placeholder: "Select a provider",
                        label: null, // Label handled outside
                        help_text: setting.description || null,
                        allow_custom: false,
                        show_refresh: true, // Set to true for provider
                        data_setting_key: setting.key,
                        disabled: !setting.editable
                    };
                    inputElement = renderCustomDropdownHTML(dropdownParams);
                } else if (setting.key === 'search.tool') {
                    const dropdownParams = {
                        input_id: setting.key,
                        dropdown_id: settingId + "-dropdown",
                        placeholder: "Select a search tool",
                        label: null,
                        help_text: setting.description || null,
                        allow_custom: false,
                        show_refresh: false, // No refresh for search tool
                        data_setting_key: setting.key,
                        disabled: !setting.editable
                    };
                    inputElement = renderCustomDropdownHTML(dropdownParams);
                } else if (setting.key === 'llm.model') { // ADD THIS ELSE IF
                    // Handle llm.model specifically within the 'select' case
                    const dropdownParams = {
                        input_id: setting.key,
                        dropdown_id: settingId + "-dropdown",
                        placeholder: "Select or enter a model",
                        label: null,
                        help_text: setting.description || null,
                        allow_custom: true, // Allow custom for model
                        show_refresh: true, // Show refresh for model
                        refresh_aria_label: "Refresh model list",
                        data_setting_key: setting.key,
                        disabled: !setting.editable
                    };
                    inputElement = renderCustomDropdownHTML(dropdownParams);
                } else {
                    // Standard select for other keys
                inputElement = `
                    <select id="${settingId}" name="${setting.key}"
                        class="settings-select form-control"
                        ${!setting.editable ? 'disabled' : ''}
                    >
                `;
                if (setting.options) {
                    setting.options.forEach(option => {
                        const selected = option.value === setting.value ? 'selected' : '';
                        inputElement += `<option value="${option.value}" ${selected}>${option.label || option.value}</option>`;
                    });
                }
                inputElement += `</select>`;
                }
                break;

            case 'checkbox':
                const checked = setting.value === true || setting.value === 'true' ? 'checked' : '';
                inputElement = `
                    <div class="settings-checkbox-container">
                        <label class="checkbox-label" for="${settingId}">
                            <input type="checkbox" id="${settingId}" name="${setting.key}"
                                class="settings-checkbox"
                                ${checked}
                                ${!setting.editable ? 'disabled' : ''}
                            >
                            <span class="checkbox-text">${setting.name}</span>
                        </label>
                    </div>
                `;
                break;

            case 'slider':
            case 'range':
                const min = setting.min_value !== null ? setting.min_value : 0;
                const max = setting.max_value !== null ? setting.max_value : 100;
                const step = setting.step !== null ? setting.step : 1;

                inputElement = `
                    <div class="settings-range-container">
                        <input type="range" id="${settingId}" name="${setting.key}"
                            class="settings-range form-control"
                            value="${setting.value !== null ? setting.value : min}"
                            min="${min}" max="${max}" step="${step}"
                            ${!setting.editable ? 'disabled' : ''}
                        >
                        <span class="settings-range-value">${setting.value !== null ? setting.value : min}</span>
                    </div>
                `;
                break;

            case 'number':
                const numMin = setting.min_value !== null ? setting.min_value : '';
                const numMax = setting.max_value !== null ? setting.max_value : '';
                const numStep = setting.step !== null ? setting.step : 1;

                inputElement = `
                    <input type="number" id="${settingId}" name="${setting.key}"
                        class="settings-input form-control"
                        value="${setting.value !== null ? setting.value : ''}"
                        min="${numMin}" max="${numMax}" step="${numStep}"
                        ${!setting.editable ? 'disabled' : ''}
                    >
                `;
                break;

            // Add a case for explicit custom dropdown if needed, or handle in default
            // case 'custom_dropdown':

            default:
                // Handle llm.model here explicitly if not handled by ui_element
                if (typeof setting.value === 'string' &&
                    (setting.value.startsWith('{') || setting.value.startsWith('['))) {
                    // Handle JSON objects/arrays rendered as textareas if not expanded
                    inputElement = `
                        <textarea id="${settingId}" name="${setting.key}"
                            class="settings-textarea json-content"
                            ${!setting.editable ? 'disabled' : ''}
                        >${setting.value}</textarea>
                    `;
                } else {
                    // Default to text input
                    inputElement = `
                        <input type="${setting.ui_element === 'password' ? 'password' : 'text'}"
                            id="${settingId}" name="${setting.key}"
                            class="settings-input form-control"
                            value="${setting.value !== null ? setting.value : ''}"
                            ${!setting.editable ? 'disabled' : ''}
                        >
                    `;
                }
                break;
        }

        // Format the setting name to be more user-friendly if it contains underscores
        let settingName = setting.name;
        if (settingName.includes('_')) {
            settingName = formatCategoryName('', settingName);
        }

        // For checkboxes, we've already handled the label in the inputElement
        if (setting.ui_element === 'checkbox') {
            return `
                <div class="settings-item form-group" data-key="${setting.key}">
                    ${inputElement}
                    ${setting.description ? `
                    <div class="input-help">
                        ${setting.description}
                    </div>
                    ` : ''}
                </div>
            `;
        }

        // For non-checkbox elements, use the standard layout without info icons
        // Ensure help text is appended correctly AFTER the input element is generated
        const helpTextHTML = setting.description ? `<div class="input-help">${setting.description}</div>` : '';

        return `
            <div class="settings-item form-group" data-key="${setting.key}">
                <div class="settings-item-header">
                    <label for="${settingId}" title="${settingName}">
                        ${settingName}
                    </label>
                </div>
                ${inputElement}
                ${helpTextHTML}
            </div>
        `;
    }

    /**
     * Render expanded JSON controls for a JSON object setting
     * @param {Object} setting - The setting object
     * @param {string} settingId - The ID for the setting
     * @param {Object} jsonObj - The parsed JSON object
     * @returns {string} - The HTML for the expanded JSON controls
     */
    function renderExpandedJsonControls(setting, settingId, jsonObj) {
        let html = `
        <div class="settings-item form-group" data-key="${setting.key}">
            <div class="settings-item-header">
                <label for="${settingId}" title="${setting.name}">
                    ${setting.name}
                </label>
            </div>
            <div class="json-expanded-controls">
                <input type="hidden" id="${settingId}_original" name="${setting.key}_original"
                    value="${JSON.stringify(jsonObj)}">

                <div class="json-property-controls">
        `;

        // Create individual form controls for each JSON property
        for (const key in jsonObj) {
            const value = jsonObj[key];
            const controlId = `${settingId}_${key}`;
            const formattedName = formatPropertyName(key);
            let controlHtml = '';

            // Create appropriate control based on value type
            if (typeof value === 'boolean') {
                controlHtml = `
                    <div class="json-property-item boolean-property" onclick="directToggleCheckbox('${controlId}')" data-checkboxid="${controlId}">
                        <div class="checkbox-wrapper">
                            <label class="checkbox-label" for="${controlId}">
                                <input type="checkbox"
                                       id="${controlId}"
                                       name="${setting.key}_${key}"
                                       class="json-property-control"
                                       data-property="${key}"
                                       data-parent-key="${setting.key}"
                                       ${value ? 'checked' : ''}
                                       ${!setting.editable ? 'disabled' : ''}>
                                <span class="checkbox-text">${formattedName}</span>
                            </label>
                        </div>
                    </div>
                `;
            } else if (typeof value === 'number') {
                controlHtml = `
                    <div class="json-property-item">
                        <label for="${controlId}" class="property-label" title="${formattedName}">${formattedName}</label>
                        <input type="number"
                               id="${controlId}"
                               name="${setting.key}_${key}"
                               class="settings-input form-control json-property-control"
                               data-property="${key}"
                               data-parent-key="${setting.key}"
                               value="${value}"
                               ${!setting.editable ? 'disabled' : ''}>
                    </div>
                `;
            } else if (typeof value === 'string' && (value === 'ITERATION' || value === 'NONE')) {
                controlHtml = `
                    <div class="json-property-item">
                        <label for="${controlId}" class="property-label" title="${formattedName}">${formattedName}</label>
                        <select id="${controlId}"
                                name="${setting.key}_${key}"
                                class="settings-select form-control json-property-control"
                                data-property="${key}"
                                data-parent-key="${setting.key}"
                                ${!setting.editable ? 'disabled' : ''}>
                            <option value="ITERATION" ${value === 'ITERATION' ? 'selected' : ''}>Iteration</option>
                            <option value="NONE" ${value === 'NONE' ? 'selected' : ''}>None</option>
                        </select>
                    </div>
                `;
            } else {
                controlHtml = `
                    <div class="json-property-item">
                        <label for="${controlId}" class="property-label" title="${formattedName}">${formattedName}</label>
                        <input type="text"
                               id="${controlId}"
                               name="${setting.key}_${key}"
                               class="settings-input form-control json-property-control"
                               data-property="${key}"
                               data-parent-key="${setting.key}"
                               value="${value}"
                               ${!setting.editable ? 'disabled' : ''}>
                    </div>
                `;
            }

            html += controlHtml;
        }

        html += `
                </div>
            </div>
            ${setting.description ? `
            <div class="input-help">
                ${setting.description}
            </div>
            ` : ''}
        </div>
        `;

        return html;
    }

    /**
     * Format property name to be more user-friendly
     * @param {string} name - The property name
     * @returns {string} - The formatted property name
     */
    function formatPropertyName(name) {
        // Replace underscores with spaces and capitalize each word
        return name.split('_')
            .map(word => word.charAt(0).toUpperCase() + word.slice(1))
            .join(' ');
    }

    /**
     * Handle settings form submission (for the entire form)
     * @param {Event} e - The submit event
     */
    function handleSettingsSubmit(e) {
        e.preventDefault();

        // Clear any previous errors
        document.querySelectorAll('.settings-error').forEach(element => {
            element.classList.remove('settings-error');
        });

        document.querySelectorAll('.settings-error-message').forEach(element => {
            element.remove();
        });

        // Collect form data
        const formData = {};

        // Get values from inputs
        document.querySelectorAll('.settings-input, .settings-textarea, .settings-select, .settings-range').forEach(input => {
            // Skip inputs that are part of expanded JSON controls
            if (input.classList.contains('json-property-control')) return;

            if (input.name) {
                // Check if value is a JSON object (textarea)
                if (input.tagName === 'TEXTAREA' && input.classList.contains('settings-textarea')) {
                    try {
                        const jsonValue = JSON.parse(input.value);
                        formData[input.name] = jsonValue;
                    } catch (e) {
                        // Mark as invalid and don't include
                        markInvalidInput(input, 'Invalid JSON format: ' + e.message);
                        return;
                    }
                } else {
                    formData[input.name] = input.value;
                }
            }
        });

        // Get values from checkboxes
        document.querySelectorAll('.settings-checkbox').forEach(checkbox => {
            // Skip checkboxes that are part of expanded JSON controls
            if (checkbox.classList.contains('json-property-control')) return;

            if (checkbox.name) {
                formData[checkbox.name] = checkbox.checked;
            }
        });

        // Process expanded JSON controls
        document.querySelectorAll('input[id$="_original"]').forEach(input => {
            if (input.name && input.name.endsWith('_original')) {
                const actualName = input.name.replace('_original', '');

                // Get all controls for this setting
                const jsonData = {};
                const controls = document.querySelectorAll(`.json-property-control[data-parent-key="${actualName}"]`);

                controls.forEach(control => {
                    const propName = control.dataset.property;

                    if (propName) {
                        if (control.type === 'checkbox') {
                            jsonData[propName] = control.checked;
                        } else if (control.tagName === 'SELECT') {
                            jsonData[propName] = control.value;
                        } else {
                            // Attempt to convert to number if appropriate
                            if (!isNaN(control.value) && control.value !== '') {
                                // Check if it should be a float or int
                                if (control.value.includes('.')) {
                                    jsonData[propName] = parseFloat(control.value);
                                } else {
                                    jsonData[propName] = parseInt(control.value, 10);
                                }
                            } else {
                                jsonData[propName] = control.value;
                            }
                        }
                    }
                });

                // Special handling for corrupted JSON values (check for empty objects, single characters, etc.)
                if (Object.keys(jsonData).length === 0) {
                    // Use the original JSON if it's non-empty and valid
                    try {
                        const originalJson = JSON.parse(input.value);
                        if (originalJson && typeof originalJson === 'object' && Object.keys(originalJson).length > 0) {
                            formData[actualName] = originalJson;
                        } else {
                            // Skip empty JSON
                            console.log(`Skipping empty JSON object for ${actualName}`);
                        }
                    } catch (e) {
                        console.log(`Error parsing original JSON for ${actualName}:`, e);
                    }
                } else {
                    // Use the collected data
                    formData[actualName] = jsonData;
                }
            }
        });

        // For report nested values that might be corrupted, ensure they're proper objects
        Object.keys(formData).forEach(key => {
            // Check for various forms of corrupted data
            if (
                (typeof formData[key] === 'string' &&
                (formData[key] === '{' ||
                 formData[key] === '[' ||
                 formData[key] === '' ||
                 formData[key] === null ||
                 formData[key] === "[object Object]")) ||
                formData[key] === null
            ) {
                // This is likely a corrupted setting
                console.log(`Detected corrupted setting: ${key} with value: ${formData[key]}`);

                if (key.startsWith('report.')) {
                    // For report settings, replace with empty object
                    formData[key] = {};
                } else {
                    // For other settings, delete to let defaults take over
                    delete formData[key];
                }
            }
        });

        // Get raw config from editor if visible
        if (rawConfigSection.style.display !== 'none' && rawConfigEditor) {
            try {
                const rawConfig = JSON.parse(rawConfigEditor.value);

                // Process raw config and flatten the structure
                const flattenedConfig = {};

                // Process each namespace in the config (app, llm, search, report)
                Object.keys(rawConfig).forEach(namespace => {
                    const section = rawConfig[namespace];

                    // Each key in the section should be added to form data with namespace prefix
                    Object.keys(section).forEach(key => {
                        const fullKey = `${namespace}.${key}`;
                        flattenedConfig[fullKey] = section[key];
                    });
                });

                // Merge with form data, giving precedence to the raw JSON config
                Object.assign(formData, flattenedConfig);
            } catch (e) {
                showAlert('Invalid JSON in raw config editor: ' + e.message, 'error');
                return;
            }
        }

        // Show saving state for the form
        if (settingsForm) {
            settingsForm.classList.add('saving');
        }

        // Submit data to API
        submitSettingsData(formData, settingsForm);
    }

    /**
     * Show a success indicator on an input
     * @param {HTMLElement} element - The input element
     */
    function showSaveSuccess(element) {
        if (!element) return;

        // Add success class
        element.classList.add('save-success');

        // Remove it after a short delay
        setTimeout(() => {
            element.classList.remove('save-success');
        }, 1500);
    }

    /**
     * Submit settings data to the API
     * @param {Object} formData - The settings to save
     * @param {HTMLElement} sourceElement - The input element that triggered the save
     */
    function submitSettingsData(formData, sourceElement) {
        // Show loading indicator
        let loadingContainer = sourceElement;

        // If it's a specific input element, find its container to position the spinner correctly
        if (sourceElement && sourceElement.tagName) {
            if (sourceElement.type === 'checkbox') {
                // For checkboxes, use the checkbox label
                loadingContainer = sourceElement.closest('.checkbox-label') || sourceElement;
            } else if (sourceElement.classList.contains('json-property-control')) {
                // For JSON property controls, use the property item
                loadingContainer = sourceElement.closest('.json-property-item') || sourceElement;
            } else {
                // For other inputs, use the form-group or settings-item
                loadingContainer = sourceElement.closest('.form-group') ||
                                  sourceElement.closest('.settings-item') ||
                                  sourceElement;
            }
        }

        // Add the saving class to show the spinner
        if (loadingContainer) {
            loadingContainer.classList.add('saving');
        }

        // Get the keys being saved for reference
        const savingKeys = Object.keys(formData);

        // Store original values to show what changed
        const originalValues = {};
        savingKeys.forEach(key => {
            const settingObj = allSettings.find(s => s.key === key);
            originalValues[key] = settingObj ? settingObj.value : null;
        });

        // --- ADD THIS LINE ---
        console.log('[submitSettingsData] Preparing to fetch /research/settings/save_all_settings with data:', JSON.stringify(formData));
        // --- END ADD ---

        fetch('/research/settings/save_all_settings', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCsrfToken()
            },
            body: JSON.stringify(formData),
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                // Show success indicator on the source element
                if (sourceElement) {
                    showSaveSuccess(sourceElement);
                }

                // Remove loading state
                if (loadingContainer) {
                    loadingContainer.classList.remove('saving');
                }

                // Update all settings data if it's a global change
                if (!sourceElement || savingKeys.length > 1) {
                    // Update global state
                    if (data.settings) {
                        allSettings = processSettings(data.settings);
                    }
                } else {
                    // Update just the changed setting in our allSettings array
                    if (savingKeys.length === 1) {
                        const key = savingKeys[0];
                        const settingIndex = allSettings.findIndex(s => s.key === key);

                        if (settingIndex !== -1 && data.settings) {
                            // Find the updated setting in the response
                            const updatedSetting = data.settings.find(s => s.key === key);

                            if (updatedSetting) {
                                // Update the setting in our array
                                allSettings[settingIndex] = processSettings([updatedSetting])[0];
                            }
                        }
                    }
                }

                // Update originalSettings cache for the saved keys
                savingKeys.forEach(key => {
                    const settingIndex = allSettings.findIndex(s => s.key === key);
                    if (settingIndex !== -1) {
                        originalSettings[key] = allSettings[settingIndex].value;
                        console.log(`Updated originalSettings cache for ${key}:`, originalSettings[key]);
                    }
                });

                // Update the raw JSON editor if it's visible
                if (rawConfigSection && rawConfigSection.style.display === 'block') {
                    prepareRawJsonEditor();
                }

                // Format a more informative message
                let successMessage = '';
                if (savingKeys.length === 1) {
                    const key = savingKeys[0];
                    const settingObj = allSettings.find(s => s.key === key);
                    const oldValue = originalValues[key];
                    const newValue = settingObj ? settingObj.value : formData[key];

                    // Format the display name for better readability
                    const displayName = key.split('.').pop().replace(/_/g, ' ');
                    const capitalizedName = displayName.charAt(0).toUpperCase() + displayName.slice(1);

                    // Format the values for display
                    const oldDisplay = formatValueForDisplay(oldValue);
                    const newDisplay = formatValueForDisplay(newValue);

                    successMessage = `${capitalizedName}: ${oldDisplay} → ${newDisplay}`;
                } else {
                    // If multiple settings were updated, use the original message
                    successMessage = data.message || 'Settings saved successfully';
                }

                // Show toast notification if ui.showMessage is available
                if (window.ui && window.ui.showMessage) {
                    window.ui.showMessage(successMessage, 'success', 3000);
                    // We're showing toast, so we pass true to skip showing the regular alert
                    showAlert(successMessage, 'success', true);
                } else {
                    // Fallback to regular alert, force showing it
                    showAlert(successMessage, 'success', false);
                }
            } else {
                // Show error message
                if (window.ui && window.ui.showMessage) {
                    window.ui.showMessage(data.message || 'Error saving settings', 'error', 5000);
                    showAlert(data.message || 'Error saving settings', 'error', true);
                } else {
                    showAlert(data.message || 'Error saving settings', 'error', false);
                }

                // Remove loading state
                if (loadingContainer) {
                    loadingContainer.classList.remove('saving');
                }
            }
        })
        .catch(error => {
            console.error('Error saving settings:', error);

            // Show error message
            if (window.ui && window.ui.showMessage) {
                window.ui.showMessage('Error saving settings: ' + error.message, 'error', 5000);
                showAlert('Error saving settings: ' + error.message, 'error', true);
            } else {
                showAlert('Error saving settings: ' + error.message, 'error', false);
            }

            // Remove loading state
            if (loadingContainer) {
                loadingContainer.classList.remove('saving');
            }
        });
    }

    /**
     * Format a value for display in notifications
     * @param {any} value - The value to format
     * @returns {string} - Formatted value for display
     */
    function formatValueForDisplay(value) {
        if (value === null || value === undefined) {
            return 'empty';
        } else if (typeof value === 'boolean') {
            return value ? 'enabled' : 'disabled';
        } else if (typeof value === 'object') {
            // For objects, show a simplified representation
            return '{...}';
        } else if (typeof value === 'string' && value.length > 20) {
            // Truncate long strings
            return `"${value.substring(0, 18)}..."`;
        } else if (typeof value === 'string') {
            return `"${value}"`;
        } else {
            return String(value);
        }
    }

    /**
     * Handle search input for filtering settings
     */
    function handleSearchInput() {
        // Only run this for the main settings dashboard
        if (!settingsContent || !settingsSearch) return;

        const searchValue = settingsSearch.value.toLowerCase();

        if (searchValue === '') {
            // If search is empty, just re-render based on active tab
            renderSettingsByTab(activeTab);
            return;
        }

        // Filter settings based on search
        const filteredSettings = allSettings.filter(setting => {
            return (
                setting.key.toLowerCase().includes(searchValue) ||
                setting.name.toLowerCase().includes(searchValue) ||
                (setting.description && setting.description.toLowerCase().includes(searchValue)) ||
                (setting.category && setting.category.toLowerCase().includes(searchValue))
            );
        });

        // Organize settings to avoid duplicate groups
        const groupedSettings = organizeSettings(filteredSettings, 'all');

        // Build HTML
        let html = '';

        // Define the order for the types
        const typeOrder = ['app', 'llm', 'search', 'report'];
        const prefixTypes = Object.keys(groupedSettings);

        // Sort prefixes by the defined order
        prefixTypes.sort((a, b) => {
            const aIndex = typeOrder.indexOf(a);
            const bIndex = typeOrder.indexOf(b);

            // If both are in the ordered list, sort by that order
            if (aIndex !== -1 && bIndex !== -1) {
                return aIndex - bIndex;
            }

            // If only one is in the list, it comes first
            if (aIndex !== -1) return -1;
            if (bIndex !== -1) return 1;

            // Alphabetically for anything else
            return a.localeCompare(b);
        });

        // For each type (app, llm, search, etc.)
        for (const type of prefixTypes) {
            // For each category in this type
            for (const category in groupedSettings[type]) {
                const sectionId = `section-${type}-${category.replace(/\s+/g, '-').toLowerCase()}`;

                html += `
                <div class="settings-section">
                    <div class="settings-section-header" data-target="${sectionId}">
                        <div class="settings-section-title" title="${category}">
                            ${category}
                        </div>
                        <div class="settings-toggle-icon">
                            <i class="fas fa-chevron-down"></i>
                        </div>
                    </div>
                    <div id="${sectionId}" class="settings-section-body">
                `;

                // Add all settings in this category
                groupedSettings[type][category].forEach(setting => {
                    html += renderSettingItem(setting);
                });

                html += `
                    </div>
                </div>
                `;
            }
        }

        if (html === '') {
            html = '<div class="empty-state"><p>No settings found matching your search</p></div>';
        }

        // Add a container for alerts that will maintain proper positioning
        html = '<div id="filtered-settings-alert" class="settings-alert-container"></div>' + html;

        // Update the content
        settingsContent.innerHTML = html;

        // Initialize accordion behavior - all expanded for search results
        initAccordions();

        // Initialize JSON handling
        initJsonFormatting();

        // Initialize range inputs
        initRangeInputs();

        // Initialize auto-save handlers after re-rendering
        initAutoSaveHandlers();

        // Initialize expanded JSON controls
        setTimeout(() => {
            initExpandedJsonControls();
        }, 100);
    }

    /**
     * Handle the reset button click
     */
    function handleReset() {
        // Reset to original values
        document.querySelectorAll('.settings-input, .settings-textarea, .settings-select').forEach(input => {
            // Skip inputs that are part of expanded JSON controls
            if (input.classList.contains('json-property-control')) return;

            const originalValue = originalSettings[input.name];

            if (typeof originalValue === 'object' && originalValue !== null) {
                input.value = JSON.stringify(originalValue, null, 2);
            } else {
                input.value = originalValue !== undefined ? originalValue : '';
            }
        });

        document.querySelectorAll('.settings-checkbox').forEach(checkbox => {
            // Skip checkboxes that are part of expanded JSON controls
            if (checkbox.classList.contains('json-property-control')) return;

            const originalValue = originalSettings[checkbox.name];
            checkbox.checked = originalValue === true || originalValue === 'true';
        });

        document.querySelectorAll('.settings-range').forEach(range => {
            const originalValue = originalSettings[range.name];
            range.value = originalValue !== undefined ? originalValue : range.min;

            // Update value display
            const valueDisplay = range.nextElementSibling;
            if (valueDisplay && valueDisplay.classList.contains('settings-range-value')) {
                valueDisplay.textContent = range.value;
            }
        });

        // Reset expanded JSON controls
        document.querySelectorAll('input[id$="_original"]').forEach(input => {
            if (input.name.endsWith('_original')) {
                const actualName = input.name.replace('_original', '');
                const originalValue = originalSettings[actualName];

                if (originalValue) {
                    // Check for corrupted JSON (single character values like "{")
                    if (typeof originalValue === 'string' && originalValue.length < 3) {
                        console.log(`Skipping corrupted JSON value for ${actualName}`);
                        return;
                    }

                    let jsonData = originalValue;
                    if (typeof jsonData === 'string') {
                        try {
                            jsonData = JSON.parse(jsonData);
                        } catch (e) {
                            console.log('Error parsing JSON during reset:', e);
                            return;
                        }
                    }

                    // Update the hidden input
                    input.value = JSON.stringify(jsonData);

                    // Update individual controls
                    for (const prop in jsonData) {
                        const control = document.querySelector(`.json-property-control[data-parent-key="${actualName}"][data-property="${prop}"]`);
                        if (control) {
                            if (control.type === 'checkbox') {
                                control.checked = !!jsonData[prop];
                            } else if (control.tagName === 'SELECT') {
                                control.value = jsonData[prop];
                            } else {
                                control.value = jsonData[prop];
                            }
                        }
                    }
                }
            }
        });

        // Format JSON values
        initJsonFormatting();

        showAlert('Settings reset to last saved values', 'info');
    }

    /**
     * Handle the reset to defaults button click
     */
    function handleResetToDefaults() {
        // Show confirmation dialog
        if (confirm('Are you sure you want to reset ALL settings to their default values? This cannot be undone.')) {
            // Call the reset to defaults API
            fetch('/research/settings/reset_to_defaults', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCsrfToken()
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.status === 'success') {
                    showAlert('Settings have been reset to defaults. Reloading page...', 'success');

                    // Reload the page after a brief delay to show the success message
                    setTimeout(() => {
                        window.location.reload();
                    }, 1500);
                } else {
                    showAlert('Error resetting settings: ' + data.message, 'error');
                }
            })
            .catch(error => {
                showAlert('Error resetting settings: ' + error, 'error');
            });
        }
    }

    /**
     * Toggle the display of raw configuration
     */
    function toggleRawConfig() {
        if (rawConfigSection && rawConfigEditor) {
            const isVisible = rawConfigSection.style.display !== 'none';

            // If hiding the editor, try to apply changes
            if (isVisible) {
                try {
                    // Parse the JSON to validate it
                    const rawConfig = JSON.parse(rawConfigEditor.value);

                    // Process and flatten the JSON
                    const flattenedConfig = {};

                    Object.keys(rawConfig).forEach(namespace => {
                        const section = rawConfig[namespace];

                        Object.keys(section).forEach(key => {
                            const fullKey = `${namespace}.${key}`;
                            flattenedConfig[fullKey] = section[key];
                        });
                    });

                    // Save the changes to apply them to UI
                    submitSettingsData(flattenedConfig, null);
                } catch (e) {
                    // Show error but don't prevent hiding the editor
                    showAlert('Invalid JSON in editor: ' + e.message, 'error');
                }
            }

            // Toggle visibility
            rawConfigSection.style.display = isVisible ? 'none' : 'block';

            // Update toggle text
            const toggleText = document.getElementById('toggle-text');
            if (toggleText) {
                toggleText.textContent = isVisible ? 'Show JSON Configuration' : 'Hide JSON Configuration';
            }

            // If showing the config, prepare it
            if (!isVisible) {
                prepareRawJsonEditor();
            }
        }
    }

    /**
     * Prepare the raw JSON editor with all settings
     */
    function prepareRawJsonEditor() {
        if (rawConfigEditor && allSettings.length > 0) {
            // Try to parse existing JSON from editor if it exists
            let existingConfig = {};
            try {
                if (rawConfigEditor.value) {
                    existingConfig = JSON.parse(rawConfigEditor.value);
                }
            } catch (e) {
                console.warn('Could not parse existing JSON config, starting fresh');
                existingConfig = {};
            }

            // Prepare settings as a JSON object
            const settingsObj = {};

            // Group by prefix (app, llm, search, report)
            allSettings.forEach(setting => {
                const key = setting.key;
                const parts = key.split('.');
                const prefix = parts[0];

                // Initialize namespace if needed
                if (!settingsObj[prefix]) {
                    settingsObj[prefix] = {};
                }

                // Parse JSON values
                let value = setting.value;
                if (typeof value === 'string' && (value.startsWith('{') || value.startsWith('['))) {
                    try {
                        value = JSON.parse(value);
                    } catch (e) {
                        // Leave as string if not valid JSON
                    }
                }

                // Add to settings object
                settingsObj[prefix][key.substring(prefix.length + 1)] = value;
            });

            // Merge with existing config to preserve unknown parameters
            Object.keys(existingConfig).forEach(prefix => {
                if (!settingsObj[prefix]) {
                    settingsObj[prefix] = {};
                }

                Object.keys(existingConfig[prefix]).forEach(key => {
                    // Only keep parameters that don't exist in our known settings
                    const fullKey = `${prefix}.${key}`;
                    const exists = allSettings.some(s => s.key === fullKey);

                    if (!exists) {
                        settingsObj[prefix][key] = existingConfig[prefix][key];
                    }
                });
            });

            // Format as pretty JSON
            rawConfigEditor.value = JSON.stringify(settingsObj, null, 2);
        }
    }

    /**
     * Function to open file location (for collections config)
     * @param {string} filePath - The file path to open
     */
    function openFileLocation(filePath) {
        // Create a hidden form and submit it to a route that will open the file location
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = "/research/open_file_location";

        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = 'file_path';
        input.value = filePath;

        form.appendChild(input);
        document.body.appendChild(form);
        form.submit();
    }

    /**
     * Initialize click handlers for checkbox wrappers
     */
    function initCheckboxWrappers() {
        // No longer needed - using direct onclick attribute instead
    }

    /**
     * Toggle checkbox directly from onclick event
     * Simple, direct function to toggle checkboxes
     * @param {string} checkboxId - The ID of the checkbox to toggle
     */
    function directToggleCheckbox(checkboxId) {
        const checkbox = document.getElementById(checkboxId);
        if (checkbox && !checkbox.disabled) {
            // Toggle the checkbox state
            checkbox.checked = !checkbox.checked;

            // Trigger change event for listeners
            const changeEvent = new Event('change', { bubbles: true });
            checkbox.dispatchEvent(changeEvent);

            // Stop event propagation
            event.stopPropagation();
        }
    }

    /**
     * Get CSRF token from meta tag
     */
    function getCsrfToken() {
        return document.querySelector('meta[name="csrf-token"]').getAttribute('content');
    }

    /**
     * Handle the fix corrupted settings button click
     */
    function handleFixCorruptedSettings() {
        // Call the fix corrupted settings API
        fetch('/research/settings/fix_corrupted_settings', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': getCsrfToken()
            }
        })
        .then(response => response.json())
        .then(data => {
            if (data.status === 'success') {
                if (data.fixed_settings && data.fixed_settings.length > 0) {
                    showAlert(`Fixed ${data.fixed_settings.length} corrupted settings. Reloading page...`, 'success');

                    // Reload the page after a brief delay to show the success message
                    setTimeout(() => {
                        window.location.reload();
                    }, 1500);
                } else {
                    showAlert('No corrupted settings were found.', 'info');
                }
            } else {
                showAlert('Error fixing corrupted settings: ' + data.message, 'error');
            }
        })
        .catch(error => {
            showAlert('Error fixing corrupted settings: ' + error, 'error');
        });
    }

    /**
     * Check if Ollama service is running
     * @returns {Promise<boolean>} True if Ollama is running
     */
    async function isOllamaRunning() {
        try {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), 5000); // 5 second timeout

            const response = await fetch('/research/settings/api/ollama-status', {
                signal: controller.signal
            });

            clearTimeout(timeoutId);

            if (response.ok) {
                const data = await response.json();
                return data.running === true;
            }
            return false;
        } catch (error) {
            console.error('Ollama check failed:', error.name === 'AbortError' ? 'Request timed out' : error);
            return false;
        }
    }

    /**
     * Fetch model providers from API
     * @param {boolean} forceRefresh - Whether to force refresh the data
     * @returns {Promise} - A promise that resolves with the model providers
     */
    function fetchModelProviders(forceRefresh = false) {
        // Use a debounce mechanism to prevent multiple calls in quick succession
        if (window.modelProvidersRequestInProgress && !forceRefresh) {
            console.log('Model providers request already in progress, using existing promise');
            return window.modelProvidersRequestInProgress;
        }

        const cachedData = getCachedData('deepResearch.modelProviders');
        const cacheTimestamp = getCachedData('deepResearch.cacheTimestamp');

        // If not forcing refresh and we have valid cached data, use it
        if (!forceRefresh && cachedData && cacheTimestamp && (Date.now() - cacheTimestamp < 3600000)) { // 1 hour cache
            console.log('Using cached model providers');
            return Promise.resolve(cachedData);
        }

        console.log('Fetching model providers from API');

        // Create a promise and store it
        window.modelProvidersRequestInProgress = fetch('/research/settings/api/available-models')
            .then(response => {
                if (!response.ok) {
                    throw new Error(`API returned status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('Got model data from API:', data);
                // Cache the data for future use
                cacheData('deepResearch.modelProviders', data);
                cacheData('deepResearch.cacheTimestamp', Date.now());

                // Process the data
                const processedData = processModelData(data);
                // Clear the request flag
                window.modelProvidersRequestInProgress = null;
                return processedData;
            })
            .catch(error => {
                console.error('Error fetching model providers:', error);
                // Clear the request flag on error
                window.modelProvidersRequestInProgress = null;
                throw error;
            });

        return window.modelProvidersRequestInProgress;
    }

    /**
     * Fetch search engines from API
     * @param {boolean} forceRefresh - Whether to force refresh the data
     * @returns {Promise} - A promise that resolves with the search engines
     */
    function fetchSearchEngines(forceRefresh = false) {
        // Use a debounce mechanism to prevent multiple calls in quick succession
        if (window.searchEnginesRequestInProgress && !forceRefresh) {
            console.log('Search engines request already in progress, using existing promise');
            return window.searchEnginesRequestInProgress;
        }

        const cachedData = getCachedData('deepResearch.searchEngines');
        const cacheTimestamp = getCachedData('deepResearch.cacheTimestamp');

        // Use cached data if available and not forcing refresh
        if (!forceRefresh && cachedData && cacheTimestamp && (Date.now() - cacheTimestamp < 3600000)) { // 1 hour cache
            console.log('Using cached search engines data');
            return Promise.resolve(cachedData);
        }

        console.log('Fetching search engines from API');

        // Create a promise and store it
        window.searchEnginesRequestInProgress = fetch('/research/settings/api/available-search-engines')
            .then(response => {
                if (!response.ok) {
                    throw new Error(`API returned status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('Received search engine data:', data);
                // Cache the data
                cacheData('deepResearch.searchEngines', data);
                cacheData('deepResearch.cacheTimestamp', Date.now());

                // Process the data
                const processedData = processSearchEngineData(data);
                // Clear the request flag
                window.searchEnginesRequestInProgress = null;
                return processedData;
            })
            .catch(error => {
                console.error('Error fetching search engines:', error);
                // Clear the request flag on error
                window.searchEnginesRequestInProgress = null;
                throw error;
            });

        return window.searchEnginesRequestInProgress;
    }

    /**
     * Process model data from API or cache
     * @param {Object} data - The model data
     */
    function processModelData(data) {
        console.log('Processing model data:', data);

        // Update MODEL_PROVIDERS with available providers from API
        if (data.provider_options && Array.isArray(data.provider_options)) {
            MODEL_PROVIDERS.length = 0; // Clear existing providers
            MODEL_PROVIDERS.push(...data.provider_options);
            console.log('Updated MODEL_PROVIDERS with available providers:', MODEL_PROVIDERS.length);
        }

        // Create a new array to store all formatted models
        const formattedModels = [];

        // Process provider options first
        if (data.provider_options) {
            console.log('Found provider options:', data.provider_options.length);
        }

        // Check for Ollama models
        if (data.providers && data.providers.ollama_models && data.providers.ollama_models.length > 0) {
            const ollama_models = data.providers.ollama_models;
            console.log('Found Ollama models:', ollama_models.length);

            // Add provider information to each model
            ollama_models.forEach(model => {
                formattedModels.push({
                    value: model.value,
                    label: model.label,
                    provider: 'OLLAMA' // Ensure provider field is added
                });
            });
        }

        // Add OpenAI models if available
        if (data.providers && data.providers.openai_models && data.providers.openai_models.length > 0) {
            const openai_models = data.providers.openai_models;
            console.log('Found OpenAI models:', openai_models.length);

            // Add provider information to each model
            openai_models.forEach(model => {
                formattedModels.push({
                    value: model.value,
                    label: model.label,
                    provider: 'OPENAI' // Ensure provider field is added
                });
            });
        }



        // Custom OpenAI Endpoint models are hidden for future development
        // This provider is not exposed in the UI but remains functional for dev use

        // Update the global modelOptions array
        modelOptions = formattedModels;
        console.log('Final modelOptions:', modelOptions.length, 'models');

        // Cache the processed models
        cacheData('deepResearch.availableModels', formattedModels);

        // Return the processed models
        return formattedModels;
    }

    /**
     * Process search engine data from API or cache
     * @param {Object} data - The search engine data
     */
    function processSearchEngineData(data) {
        console.log('Processing search engine data:', data);
        if (data.engine_options && data.engine_options.length > 0) {
            searchEngineOptions = data.engine_options;
            console.log('Updated search engine options:', searchEngineOptions);

            // Always initialize search engine dropdowns when receiving new data
            initializeSearchEngineDropdowns();
        } else {
            console.warn('No engine options found in search engine data');
        }
    }

    /**
     * Initialize custom model dropdowns in the LLM section
     */
    function initializeModelDropdowns() {
        console.log('Initializing model dropdowns');

        // Use getElementById for direct access
        const settingsProviderInput = document.getElementById('llm.provider');
        const settingsModelInput = document.getElementById('llm.model');
        const providerHiddenInput = document.getElementById('llm.provider_hidden');
        const modelHiddenInput = document.getElementById('llm.model_hidden');
        const providerDropdownList = document.getElementById('setting-llm-provider-dropdown-list');
        const modelDropdownList = document.getElementById('setting-llm-model-dropdown-list');

        // Skip if already initialized (avoid redundant calls)
        if (window.modelDropdownsInitialized) {
            console.log('Model dropdowns already initialized, skipping');
            return;
        }

        console.log('Found model elements:', {
            settingsProviderInput: !!settingsProviderInput,
            settingsModelInput: !!settingsModelInput,
            providerHiddenInput: !!providerHiddenInput,
            modelHiddenInput: !!modelHiddenInput,
            providerDropdownList: !!providerDropdownList,
            modelDropdownList: !!modelDropdownList
        });

        // Check if elements exist before proceeding
        if (!settingsProviderInput || !providerDropdownList || !providerHiddenInput) {
            console.warn('LLM Provider input, dropdown list, or hidden input element not found. Skipping provider initialization.');
            return; // Don't proceed if required elements are missing
        }

        if (!settingsModelInput || !modelDropdownList || !modelHiddenInput) {
            console.warn('LLM Model input, dropdown list, or hidden input element not found. Skipping model initialization.');
            return; // Don't proceed if required elements are missing
        }

        // Mark as initialized to prevent redundant setup
        window.modelDropdownsInitialized = true;

        // Load model options first
        loadModelOptions().then(() => {
            console.log(`Models loaded, available options: ${modelOptions.length}`);

            // Get current settings from hidden inputs
            const currentProvider = providerHiddenInput.value || 'openai';
            const currentModel = modelHiddenInput.value || 'gpt-4o-mini';

            console.log('Current settings:', { provider: currentProvider, model: currentModel });

            // Setup provider dropdown
            if (settingsProviderInput && providerDropdownList && window.setupCustomDropdown) {
                // Set hidden input value first for provider (prevents race conditions)
                if (providerHiddenInput) {
                    console.log('Set provider hidden input value:', currentProvider);
                    providerHiddenInput.value = currentProvider;
                }

                // Set hidden input value for model too
                if (modelHiddenInput) {
                    console.log('Set model hidden input value:', currentModel);
                    modelHiddenInput.value = currentModel;
                }

                // If there are available options, create or update the dropdowns
                if (MODEL_PROVIDERS && MODEL_PROVIDERS.length > 0) {
                    // Cache references to DOM elements to prevent lookups
                    const providerList = providerDropdownList;

                    // Create provider dropdown
                    const providerDropdown = window.setupCustomDropdown(
                        settingsProviderInput,
                        providerList,
                        () => MODEL_PROVIDERS,
                        (value, item) => {
                            console.log('Provider selected:', value);

                            // Update hidden input
                            if (providerHiddenInput) {
                                providerHiddenInput.value = value;

                                // Trigger filtering of model options
                                filterModelOptionsForProvider(value);

                                // Save to localStorage
                                localStorage.setItem('lastUsedProvider', value);

                                // Trigger save
                                const changeEvent = new Event('change', { bubbles: true });
                                providerHiddenInput.dispatchEvent(changeEvent);
                            }
                        },
                        false // Don't allow custom values
                    );

                    // Set initial value
                    if (currentProvider && providerDropdown.setValue) {
                        console.log('Setting initial provider:', currentProvider);
                        providerDropdown.setValue(currentProvider, false); // Don't fire event
                        // Explicitly set hidden input value on init
                        providerHiddenInput.value = currentProvider.toLowerCase();
                    }

                    // --- ADD CHANGE LISTENER TO HIDDEN INPUT ---
                    providerHiddenInput.removeEventListener('change', handleInputChange); // Remove old listener first
                    providerHiddenInput.addEventListener('change', handleInputChange);
                    console.log('Added change listener to hidden provider input:', providerHiddenInput.id);
                    // --- END OF ADDED LISTENER ---
                }
            }

            // Create model dropdown with full list of models first
            if (settingsModelInput && modelDropdownList && modelHiddenInput && window.setupCustomDropdown) {
                // Initialize the dropdown with ALL models first, don't filter yet
                const modelDropdownControl = window.setupCustomDropdown(
                    settingsModelInput,
                    modelDropdownList,
                    () => modelOptions.length > 0 ? modelOptions : [],
                    (value, item) => {
                        console.log('Model selected:', value);

                        // Update hidden input
                        if (modelHiddenInput) {
                            modelHiddenInput.value = value;

                            // Save to localStorage
                            localStorage.setItem('lastUsedModel', value);
                        }
                    },
                    true // Allow custom values
                );

                // Set initial model value
                if (modelDropdownControl) {
                    // Set the current model without filtering first
                    if (currentModel) {
                        console.log('Setting initial model:', currentModel);
                        modelDropdownControl.setValue(currentModel, false); // Don't fire event
                        // Explicitly set hidden input value on init
                        modelHiddenInput.value = currentModel;
                    }

                    // Now filter models for the current provider - AFTER setting the initial value
                    setTimeout(() => {
                        filterModelOptionsForProvider(currentProvider);
                    }, 100); // Small delay to ensure value is set first

                    // --- ADD CHANGE LISTENER TO HIDDEN INPUT ---
                    modelHiddenInput.removeEventListener('change', handleInputChange); // Remove old listener first
                    modelHiddenInput.addEventListener('change', handleInputChange);
                    console.log('Added change listener to hidden model input:', modelHiddenInput.id);
                    // --- END OF ADDED LISTENER ---
                }

                // Set up refresh button
                const refreshBtn = document.querySelector('#llm-model-refresh');
                if (refreshBtn) {
                    refreshBtn.addEventListener('click', function() {
                        const icon = refreshBtn.querySelector('i');
                        if (icon) icon.className = 'fas fa-spinner fa-spin';

                        // Force refresh models
                        loadModelOptions(true).then(() => {
                            if (icon) icon.className = 'fas fa-sync-alt';

                            // Re-filter for current provider
                            const currentProvider = providerHiddenInput ?
                                providerHiddenInput.value :
                                settingsProviderInput ? settingsProviderInput.value : 'openai';

                            filterModelOptionsForProvider(currentProvider);

                            showAlert('Model list refreshed', 'success');
                        }).catch(error => {
                            console.error('Error refreshing models:', error);
                            if (icon) icon.className = 'fas fa-sync-alt';
                            showAlert('Failed to refresh models: ' + error.message, 'error');
                        });
                    });
                }
            }

            // Set up provider change listener after everything is initialized
            setupProviderChangeListener();
        }).catch(err => {
            console.error('Error initializing model dropdowns:', err);
            // Show a warning to the user
            showAlert('Failed to load model options. Using fallback values.', 'warning');
        });
    }

    /**
     * Add fallback model based on provider
     */
    function addFallbackModel(provider, hiddenInput, visibleInput) {
        let fallbackModel = '';
        let displayName = '';

        const providerLower = provider ? provider.toLowerCase() : '';
        const providerUpper = provider ? provider.toUpperCase() : '';

        if (providerLower === 'ollama' || providerUpper === 'OLLAMA') {
            // No static fallback for Ollama - only use dynamically fetched models
            fallbackModel = '';
            displayName = 'No models available (Ollama not running)';
        } else if (providerLower === 'openai' || providerUpper === 'OPENAI') {
            fallbackModel = 'gpt-4o-mini';
            displayName = 'GPT-4o Mini (OpenAI)';
        } else if (providerLower === 'lmstudio' || providerUpper === 'LMSTUDIO') {
            // No static fallback for LM Studio - only use dynamically fetched models
            fallbackModel = '';
            displayName = 'No models available (LM Studio not running)';
        } else {
            fallbackModel = 'gpt-4o-mini';
            displayName = 'GPT-4o Mini (Default)';
        }

        if (hiddenInput && fallbackModel) {
            hiddenInput.value = fallbackModel;
        }

        if (visibleInput) {
            visibleInput.value = displayName;
        }
    }

    /**
     * Initialize custom search engine dropdowns
     */
    function initializeSearchEngineDropdowns() {
        console.log('Initializing search engine dropdown');
        // Check for the search engine input field
        const searchEngineInput = document.getElementById('search.tool');
        const searchEngineHiddenInput = document.getElementById('search.tool_hidden');
        const dropdownList = document.getElementById('setting-search-tool-dropdown-list');

        // Skip if already initialized (avoid redundant calls)
        if (window.searchEngineDropdownInitialized) {
            console.log('Search engine dropdown already initialized, skipping');
            return;
        }

        console.log('Found search engine elements:', {
            searchEngineInput: !!searchEngineInput,
            searchEngineHiddenInput: !!searchEngineHiddenInput,
            dropdownList: !!dropdownList
        });

        if (!searchEngineInput || !dropdownList || !searchEngineHiddenInput) {
            console.warn('Search engine input, hidden input, or dropdown list not found. Skipping initialization.');
            return; // Exit early if required elements are missing
        }

        // Mark as initialized to prevent redundant calls
        window.searchEngineDropdownInitialized = true;

        // Set up the dropdown
        if (window.setupCustomDropdown) {
            const dropdown = window.setupCustomDropdown(
                searchEngineInput,
                dropdownList,
                () => searchEngineOptions.length > 0 ? searchEngineOptions : [{ value: 'auto', label: 'Auto (Default)' }],
                (value, item) => {
                    console.log('Search engine selected:', value);
                    // Update the hidden input value
                    searchEngineHiddenInput.value = value;
                    // Trigger a change event on the hidden input to save
                    const changeEvent = new Event('change', { bubbles: true });
                    searchEngineHiddenInput.dispatchEvent(changeEvent);
                    // Save to localStorage
                    localStorage.setItem('lastUsedSearchEngine', value);
                },
                false, // Don't allow custom values
                'No search engines available.'
            );

            // Get current value
            let currentValue = '';
            if (typeof allSettings !== 'undefined' && Array.isArray(allSettings)) {
                const currentSetting = allSettings.find(s => s.key === 'search.tool');
                if (currentSetting) {
                    currentValue = currentSetting.value || '';
                }
            }
            if (!currentValue) {
                currentValue = localStorage.getItem('lastUsedSearchEngine') || 'auto';
            }

            // Set initial value
            if (currentValue && dropdown.setValue) {
                console.log('Setting initial search engine value:', currentValue);
                dropdown.setValue(currentValue, false);
                searchEngineHiddenInput.value = currentValue;
            }

            // --- ADD CHANGE LISTENER TO HIDDEN INPUT ---
            searchEngineHiddenInput.removeEventListener('change', handleInputChange); // Remove old listener first
            searchEngineHiddenInput.addEventListener('change', handleInputChange);
            console.log('Added change listener to hidden search engine input:', searchEngineHiddenInput.id);
            // --- END OF ADDED LISTENER ---
        }
    }

    /**
     * Process settings to handle object values
     */
    function processSettings(settings) {
        // Convert to a list.
        const settingsList = [];
        for (const key in settings) {
            const setting = settings[key];
            setting["key"] = key
            settingsList.push(setting);
        }

        return settingsList.map(setting => {
            const processedSetting = {...setting};

            // Convert object values to JSON strings for display
            if (typeof processedSetting.value === 'object' && processedSetting.value !== null) {
                processedSetting.value = JSON.stringify(processedSetting.value, null, 2);
            }

            // Handle corrupted JSON values (e.g., just "{" or "[" or "[object Object]")
            if (typeof processedSetting.value === 'string' &&
                (processedSetting.value === '{' ||
                 processedSetting.value === '[' ||
                 processedSetting.value === '{}' ||
                 processedSetting.value === '[]' ||
                 processedSetting.value === '[object Object]')) {

                console.log(`Detected corrupted JSON value for ${processedSetting.key}: ${processedSetting.value}`);

                // Initialize with empty object for corrupted JSON values
                if (processedSetting.key.startsWith('report.')) {
                    processedSetting.value = '{}';
                }
            }

            return processedSetting;
        });
    }

    /**
     * Add CSS styles for loading indicators and saved state
     */
    function addDynamicStyles() {
        // Create a style element if it doesn't exist
        let styleEl = document.getElementById('settings-dynamic-styles');
        if (!styleEl) {
            styleEl = document.createElement('style');
            styleEl.id = 'settings-dynamic-styles';
            document.head.appendChild(styleEl);
        }

        // Add CSS for saving and success states
        styleEl.textContent = `
            .saving {
                opacity: 0.7;
                pointer-events: none;
                position: relative;
            }

            .saving::after {
                content: '';
                position: absolute;
                top: 50%;
                right: 10px;
                width: 16px;
                height: 16px;
                margin-top: -8px;
                border: 2px solid rgba(0, 123, 255, 0.1);
                border-top-color: #007bff;
                border-radius: 50%;
                animation: spinner 0.8s linear infinite;
                z-index: 10;
            }

            .save-success {
                border-color: #28a745 !important;
                transition: border-color 0.3s;
            }

            @keyframes spinner {
                to { transform: rotate(360deg); }
            }

            .spinner {
                width: 40px;
                height: 40px;
                border: 3px solid rgba(255, 255, 255, 0.1);
                border-radius: 50%;
                border-top-color: var(--accent-primary);
                animation: spin 1s ease-in-out infinite;
                margin: 0 auto 1rem auto;
                display: block;
            }

            .settings-item .checkbox-label {
                margin-top: 8px;
                padding-left: 0;
            }

            // Add styles for the loading spinner
            const spinnerStyles =
                '.saving {' +
                '    position: relative;' +
                '}' +
                '' +
                '.saving:before {' +
                '    content: \'\';' +
                '    position: absolute;' +
                '    left: -25px;' +
                '    top: 50%;' +
                '    transform: translateY(-50%);' +
                '    width: 16px;' +
                '    height: 16px;' +
                '    border: 2px solid rgba(255, 255, 255, 0.3);' +
                '    border-radius: 50%;' +
                '    border-top-color: #fff;' +
                '    animation: spinner .6s linear infinite;' +
                '    z-index: 10;' +
                '}' +
                '' +
                '.checkbox-label.saving:before {' +
                '    left: -25px;' +
                '    top: 50%;' +
                '}' +
                '' +
                '@keyframes spinner {' +
                '    to {transform: translateY(-50%) rotate(360deg);}' +
                '}';

            // Add the styles to the head
            const style = document.createElement('style');
            style.textContent = spinnerStyles;
            document.head.appendChild(style);
        `;
    }

    // Initialize dynamic styles
    addDynamicStyles();

    /**
     * Initialize the settings component
     */
    function initializeSettings() {
        // Get DOM elements
        settingsForm = document.querySelector('form');
        settingsContent = document.getElementById('settings-content');
        settingsSearch = document.getElementById('settings-search');
        settingsTabs = document.querySelectorAll('.settings-tab');
        settingsAlert = document.getElementById('settings-alert');
        rawConfigToggle = document.getElementById('toggle-raw-config');
        rawConfigSection = document.getElementById('raw-config');
        rawConfigEditor = document.getElementById('raw_config_editor');

        // Add dynamic styles immediately
        addDynamicStyles();

        // Initialize range inputs to display their values
        initRangeInputs();

        // Initialize accordion behavior
        initAccordions();

        // Initialize JSON handling
        initJsonFormatting();

        // Load settings from API if on settings dashboard
        if (settingsContent) {
            // First fetch the model and search engine data to have it ready
            // when needed, but don't wait for it to complete
            Promise.all([fetchModelProviders(), fetchSearchEngines()]).then(() => {
                // Then load settings
                loadSettings();
            }).catch(err => {
                console.error("Error fetching providers/engines initially", err);
                // Still try to load settings even if fetching options fails
                loadSettings();
            });
        }

        // Handle tab switching
        if (settingsTabs) {
            settingsTabs.forEach(tab => {
                tab.addEventListener('click', () => {
                    // Remove active class from all tabs
                    settingsTabs.forEach(t => t.classList.remove('active'));

                    // Add active class to clicked tab
                    tab.classList.add('active');

                    // Update active tab and re-render
                    activeTab = tab.dataset.tab;
                    renderSettingsByTab(activeTab);

                    // Set a small timeout to ensure DOM is ready before initializing
                    setTimeout(() => {
                        // Initialize dropdowns after rendering content
                        // Moved dropdown init inside loadSettings success callback
                        // if (activeTab === 'llm' || activeTab === 'all') {
                        //     initializeModelDropdowns();
                        // }
                        // if (activeTab === 'search' || activeTab === 'all') {
                        //     initializeSearchEngineDropdowns();
                        // }

                        // Re-initialize auto-save handlers after tab switch and render
                        initAutoSaveHandlers();
                        // Setup refresh buttons after dropdowns might have been created
                        setupRefreshButtons();
                    }, 100); // Reduced timeout slightly
                });
            });
        }

        // Handle search filtering
        if (settingsSearch) {
            settingsSearch.addEventListener('input', handleSearchInput);
        }

        // Handle reset to defaults button
        const resetToDefaultsButton = document.getElementById('reset-to-defaults-button');
        if (resetToDefaultsButton) {
            resetToDefaultsButton.addEventListener('click', handleResetToDefaults);
        }

        // Handle raw config toggle
        if (rawConfigToggle) {
            rawConfigToggle.addEventListener('click', toggleRawConfig);
        }

        // Initialize specific settings page form handlers
        initSpecificSettingsForm();

        // Handle form submission
        if (settingsForm) {
            settingsForm.addEventListener('submit', handleSettingsSubmit);
        }

        // Add click handler for the logo to navigate home
        const logoLink = document.getElementById('logo-link');
        if (logoLink) {
            logoLink.addEventListener('click', () => {
                window.location.href = '/research/';
            });
        }

        // --- MODIFICATION START: Call initAutoSaveHandlers at the end of initializeSettings ---
        // Initialize auto-save handlers after all other setup
        initAutoSaveHandlers();
        // --- MODIFICATION END ---
    }

    // Initialize on DOM content loaded
    // --- MODIFICATION START: Ensure initialization order ---
    // Ensure initialization happens after DOM content is loaded
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeSettings);
    } else {
        // DOM is already loaded, run initialize
        initializeSettings();
    }
    // --- MODIFICATION END ---

    // Expose the setupCustomDropdowns function for other modules to use
    window.setupSettingsDropdowns = initializeModelDropdowns;

    /**
     * Show an alert message at the top of the settings form
     * @param {string} message - The message to display
     * @param {string} type - The alert type: success, error, warning, info
     * @param {boolean} skipIfToastShown - Whether to skip showing this alert if a toast was already shown
     */
    function showAlert(message, type, skipIfToastShown = true) {
        // If window.ui.showAlert exists, use it
        if (window.ui && window.ui.showAlert) {
            window.ui.showAlert(message, type, skipIfToastShown);
            return;
        }

        // Otherwise fallback to old implementation (this shouldn't happen once ui.js is loaded)
        // If we're showing a toast and we want to skip the regular alert, just return
        if (skipIfToastShown && window.ui && window.ui.showMessage) {
            return;
        }

        // Find the alert container - look for filtered settings alert first
        let alertContainer = document.getElementById('filtered-settings-alert');

        // If not found, fall back to the regular alert
        if (!alertContainer) {
            alertContainer = document.getElementById('settings-alert');
        }

        if (!alertContainer) return;

        // Clear any existing alerts
        alertContainer.innerHTML = '';

        // Create alert element
        const alert = document.createElement('div');
        alert.className = `alert alert-${type}`;
        alert.innerHTML = `<i class="fas ${type === 'success' ? 'fa-check-circle' : 'fa-exclamation-circle'}"></i> ${message}`;

        // Add a close button
        const closeBtn = document.createElement('span');
        closeBtn.className = 'alert-close';
        closeBtn.innerHTML = '&times;';
        closeBtn.addEventListener('click', () => {
            alert.remove();
            alertContainer.style.display = 'none';
        });

        alert.appendChild(closeBtn);

        // Add to container
        alertContainer.appendChild(alert);
        alertContainer.style.display = 'block';

        // Auto-hide after 5 seconds
        setTimeout(() => {
            alert.remove();
            if (alertContainer.children.length === 0) {
                alertContainer.style.display = 'none';
            }
        }, 5000);
    }

    /**
     * Set up custom dropdowns for settings
     */
    function setupCustomDropdowns() {
        // Find all custom dropdowns in the settings form
        const customDropdowns = document.querySelectorAll('.custom-dropdown');

        // Process each dropdown
        customDropdowns.forEach(dropdown => {
            const dropdownInput = dropdown.querySelector('.custom-dropdown-input');
            const dropdownList = dropdown.querySelector('.custom-dropdown-list');

            if (!dropdownInput || !dropdownList) return;

            // Get the setting key from the data attribute or input ID
            const settingKey = dropdownInput.getAttribute('data-setting-key') || dropdownInput.id;
            if (!settingKey) return;

            console.log('Setting up custom dropdown for:', settingKey);

            // Get current setting value from settings or localStorage
            let currentValue = '';

            // Try to get from allSettings first if available
            if (typeof allSettings !== 'undefined' && Array.isArray(allSettings)) {
            const currentSetting = allSettings.find(s => s.key === settingKey);
                if (currentSetting) {
                    currentValue = currentSetting.value || '';
                }
            }

            // Fallback to localStorage values if we don't have a value yet
            if (!currentValue) {
                if (settingKey === 'llm.model') {
                    currentValue = localStorage.getItem('lastUsedModel') || '';
                } else if (settingKey === 'llm.provider') {
                    currentValue = localStorage.getItem('lastUsedProvider') || '';
                } else if (settingKey === 'search.tool') {
                    currentValue = localStorage.getItem('lastUsedSearchEngine') || '';
                }
            }

            // Get the hidden input
            const hiddenInput = document.getElementById(`${dropdownInput.id}_hidden`);
            if (!hiddenInput) {
                console.warn(`Hidden input not found for dropdown: ${dropdownInput.id}`);
                return; // Skip if hidden input doesn't exist
            }

            // Set up options source based on setting key
            let optionsSource = [];
            let allowCustom = false;

            if (settingKey === 'llm.model') {
                // For model dropdown, use dynamically fetched model options only
                optionsSource = typeof modelOptions !== 'undefined' && modelOptions.length > 0 ?
                    modelOptions : [];
                allowCustom = true;

                // Set up refresh button if it exists
                const refreshBtn = dropdown.querySelector('.dropdown-refresh-button');
                if (refreshBtn) {
                    refreshBtn.addEventListener('click', function() {
                        const icon = refreshBtn.querySelector('i');
                        if (icon) icon.className = 'fas fa-spinner fa-spin';

                        // Force refresh of model options
                        if (typeof loadModelOptions === 'function') {
                            loadModelOptions(true).then(() => {
                                if (icon) icon.className = 'fas fa-sync-alt';

                                // Force dropdown update
                                const event = new Event('click', { bubbles: true });
                                dropdownInput.dispatchEvent(event);
                            }).catch(error => {
                                console.error('Error refreshing models:', error);
                                if (icon) icon.className = 'fas fa-sync-alt';
                                if (typeof showAlert === 'function') {
                                    showAlert('Failed to refresh models: ' + error.message, 'error');
                                }
                            });
                        } else {
                            if (icon) icon.className = 'fas fa-sync-alt';
                        }
                    });
                }
            } else if (settingKey === 'llm.provider') {
                // Special handling for provider dropdown - use dynamically loaded providers
                // Fallback to basic providers if MODEL_PROVIDERS is not yet loaded
                const LOCAL_MODEL_PROVIDERS = [
                    { value: 'openai', label: 'OpenAI API (Default)' }
                    // Ollama and LM Studio only shown if available at runtime
                ];

                // Use MODEL_PROVIDERS if available (populated from API), otherwise use fallback
                if (MODEL_PROVIDERS && MODEL_PROVIDERS.length > 0) {
                    optionsSource = MODEL_PROVIDERS;
                } else {
                    // Try to get options from settings if available, otherwise use fallback
                    if (typeof allSettings !== 'undefined' && Array.isArray(allSettings)) {
                        const availableProviders = allSettings.find(s => s.key === 'llm.provider');
                        if (availableProviders && availableProviders.options && availableProviders.options.length > 0) {
                            optionsSource = availableProviders.options.map(opt => ({
                                value: opt.value,
                                label: opt.label
                            }));
                        } else {
                            optionsSource = LOCAL_MODEL_PROVIDERS;
                        }
                    } else {
                        optionsSource = LOCAL_MODEL_PROVIDERS;
                    }
                }
            } else if (settingKey === 'search.tool') {
                optionsSource = typeof searchEngineOptions !== 'undefined' && searchEngineOptions.length > 0 ?
                    searchEngineOptions : [
                        { value: 'google_pse', label: 'Google Programmable Search' },
                        { value: 'duckduckgo', label: 'DuckDuckGo' },
                        { value: 'auto', label: 'Auto (Default)' }
                    ];
            }

            console.log(`Setting up dropdown for ${settingKey} with ${optionsSource.length} options`);

            // Initialize the dropdown
            if (window.setupCustomDropdown) {
                const dropdown = window.setupCustomDropdown(
                    dropdownInput,
                    dropdownList,
                    () => optionsSource,
                    (value, item) => {
                        console.log(`Dropdown ${settingKey} selected:`, value);
                        // --- MODIFICATION START: Removed hiddenInput retrieval, already have it ---
                        const hiddenInput = document.getElementById(`${dropdownInput.id}_hidden`);
                        // --- MODIFICATION END ---

                        // --- MODIFICATION START: Update hidden input and trigger change ---
                        if (hiddenInput) {
                            hiddenInput.value = value;
                            const changeEvent = new Event('change', { bubbles: true });
                            hiddenInput.dispatchEvent(changeEvent);
                        }
                        // --- MODIFICATION END ---

                        // For provider changes, update model options
                        if (settingKey === 'llm.provider' && typeof filterModelOptionsForProvider === 'function') {
                            filterModelOptionsForProvider(value);
                        }

                        // Save to localStorage for persistence
                        if (settingKey === 'llm.model') {
                            localStorage.setItem('lastUsedModel', value);
                        } else if (settingKey === 'llm.provider') {
                            localStorage.setItem('lastUsedProvider', value);
                        } else if (settingKey === 'search.tool') {
                            localStorage.setItem('lastUsedSearchEngine', value);
                        }
                    },
                    allowCustom
                );

                // Set initial value
                if (currentValue && dropdown.setValue) {
                    console.log(`Setting initial value for ${settingKey}:`, currentValue);
                    dropdown.setValue(currentValue, false); // Don't fire event on init
                    // --- MODIFICATION START: Set hidden input initial value ---
                    if (hiddenInput) {
                        hiddenInput.value = currentValue;
                        console.log('Set initial hidden input value for', settingKey, 'to', currentValue);
                    }
                    // --- MODIFICATION END ---
                }

                // --- MODIFICATION START: Add listener to hidden input in initAutoSaveHandlers ---
                // The listener is added globally in initAutoSaveHandlers now.
                // Ensure initAutoSaveHandlers is called *after* setupCustomDropdowns.
                // --- MODIFICATION END ---
            }
        });

        // --- MODIFICATION START: Call initAutoSaveHandlers after setup ---
        // Ensure initAutoSaveHandlers is called after dropdowns are set up
        // It might be better to call initAutoSaveHandlers once after all rendering and setup is done.
        // Let's move the call within initializeSettings() to ensure order.
        initAutoSaveHandlers();
        // --- MODIFICATION END ---
    }

    /**
     * Filter model options based on the selected provider
     * @param {string} provider - The provider to filter models by
     */
    function filterModelOptionsForProvider(provider) {
        const providerLower = provider ? provider.toLowerCase() : ''; // Use lowercase for consistency
        const providerUpper = provider ? provider.toUpperCase() : ''; // Keep for legacy compatibility
        console.log('Filtering models for provider:', providerLower);

        // Get model dropdown elements using ID
        const modelInput = document.getElementById('llm.model');
        const modelDropdownList = document.getElementById('setting-llm-model-dropdown-list'); // Correct ID based on template generation
        const modelHiddenInput = document.getElementById('llm.model_hidden');

        if (!modelInput || !modelDropdownList) { // Use correct variable name
            console.warn('Model input or list not found when filtering.');
            return;
        }

        // Check if dropdown is currently open
        const isDropdownOpen = window.getComputedStyle(modelDropdownList).display !== 'none';
        console.log('Dropdown is currently:', isDropdownOpen ? 'open' : 'closed');

        // Filter the models based on provider
        const filteredModels = modelOptions.filter(model => {
            if (!model || typeof model !== 'object') return false;

            // For Ollama, use more flexible matching due to model name variations
            if (providerLower === 'ollama' || providerUpper === 'OLLAMA') {
                // Check model provider property first (handle both cases)
                if (model.provider && (model.provider.toLowerCase() === 'ollama' || model.provider.toUpperCase() === 'OLLAMA')) {
                    return true;
                }

                // Check label for Ollama mentions
                if (model.label && model.label.toUpperCase().includes('OLLAMA')) {
                    return true;
                }

                // Check value for common Ollama model name patterns
                if (model.value) {
                    const value = model.value.toLowerCase();
                    // Common Ollama model name patterns
                    if (value.includes('llama') || value.includes('mistral') ||
                        value.includes('gemma') || value.includes('falcon') ||
                        value.includes('codellama') || value.includes('phi')) {
                        return true;
                    }
                }

                return false;
            }

            // OPENAI_ENDPOINT provider is hidden for future development

            // For other providers, use standard matching (handle both cases)
            if (model.provider) {
                return model.provider.toLowerCase() === providerLower || model.provider.toUpperCase() === providerUpper;
            }

            // If provider is missing, check label for provider hints
            if (model.label) {
                const label = model.label.toUpperCase();
                if ((providerLower === 'openai' || providerUpper === 'OPENAI') && label.includes('OPENAI'))
                    return true;
                if ((providerLower === 'lmstudio' || providerUpper === 'LMSTUDIO') && label.includes('LM STUDIO'))
                    return true;
            }

            return false;
        });

        console.log(`Filtered models for ${providerUpper}:`, filteredModels.length, 'models');

        // Try to update the dropdown options without reinitializing if possible
        if (window.updateDropdownOptions && typeof window.updateDropdownOptions === 'function') {
            console.log('Using updateDropdownOptions to preserve dropdown state');
            window.updateDropdownOptions(modelInput, filteredModels);

            // Try to maintain the current selection if applicable
            const currentModel = modelHiddenInput ? modelHiddenInput.value : null;
            if (currentModel) {
                // Check if current model is valid for this provider
                const isValid = filteredModels.some(m => m.value === currentModel);
                if (!isValid && filteredModels.length > 0) {
                    // Select first available model if current is not valid
                    const firstModel = filteredModels[0].value;
                    console.log(`Current model ${currentModel} invalid for provider ${providerUpper}. Setting to first available: ${firstModel}`);
                    modelHiddenInput.value = firstModel;
                    modelInput.value = filteredModels[0].label || firstModel;
                }
            }

            // If dropdown was open, ensure it stays open
            if (isDropdownOpen) {
                setTimeout(() => {
                    if (modelDropdownList.style.display === 'none') {
                        console.log('Reopening dropdown that was closed during update');
                        modelDropdownList.style.display = 'block';
                    }
                }, 50);
            }

            return;
        }

        // Backup method - reinitialize the dropdown but try to preserve open state
        if (window.setupCustomDropdown) {
            console.log('Reinitializing model dropdown with filtered models');

            // Store the returned control object
            const modelDropdownControl = window.setupCustomDropdown(
                modelInput,
                modelDropdownList, // Use correct variable name
                () => filteredModels.length > 0 ? filteredModels : [
                    { value: 'no-models', label: 'No models available for this provider' }
                ],
                (value, item) => {
                    console.log('Selected model:', value);
                    // Save the selection
                    if (modelHiddenInput) { // Use the variable we already have
                        modelHiddenInput.value = value;

                        // Trigger change event to save
                        const changeEvent = new Event('change', { bubbles: true });
                        modelHiddenInput.dispatchEvent(changeEvent);
                    }
                },
                true // Allow custom values
            );

            // Try to maintain the current selection if applicable
            const currentModel = modelHiddenInput ? modelHiddenInput.value : null;

            if (currentModel && modelDropdownControl && modelDropdownControl.setValue) {
                // Check if current model is valid for this provider
                const isValid = filteredModels.some(m => m.value === currentModel);
                if (isValid) {
                    console.log(`Setting model value to currently selected: ${currentModel}`);
                    modelDropdownControl.setValue(currentModel, false);
                } else {
                    // Select first available model
                    // *** FIX: Check if filteredModels has elements ***
                    if (filteredModels.length > 0) {
                        const firstModel = filteredModels[0].value;
                        console.log(`Current model ${currentModel} invalid for provider ${providerUpper}. Setting to first available: ${firstModel}`);
                        modelDropdownControl.setValue(firstModel, false); // DON'T fire event, avoid loop
                    } else {
                        // No models available, clear the input
                        console.log(`No models found for provider ${providerUpper}. Clearing model selection.`);
                        modelDropdownControl.setValue("", false);
                    }
                }
            }

            // If dropdown was open, force it to reopen
            if (isDropdownOpen) {
                setTimeout(() => {
                    console.log('Reopening dropdown that was closed during reinitialization');
                    modelDropdownList.style.display = 'block';
                }, 100);
            }
        }

        // Also update any provider-dependent UI
        updateProviderDependentUI(providerUpper);
    }

    /**
     * Update any UI elements that depend on the provider selection
     */
    function updateProviderDependentUI(provider) {
        // Custom endpoint UI is hidden for future development
        // No provider-dependent UI elements currently visible
    }

    /**
     * Set up event listener for provider changes to update model options
     */
    function setupProviderChangeListener() {
        console.log('Setting up provider change listener');
        const providerInput = document.getElementById('llm.provider'); // Use ID selector
        const providerHiddenInput = document.getElementById('llm.provider_hidden');

        // Function to handle the change
        const handleProviderChange = (selectedValue) => {
            console.log('Provider changed to:', selectedValue);
            if (typeof filterModelOptionsForProvider === 'function') {
                filterModelOptionsForProvider(selectedValue);
            }
            // Update other UI elements if needed
            updateProviderDependentUI(selectedValue ? selectedValue.toUpperCase() : '');
            // No need to explicitly save here, the main auto-save handler for hidden input does it
        };

        if (providerHiddenInput) {
            // Monitor the hidden input for changes (triggered by custom dropdown selection)
            providerHiddenInput.removeEventListener('change', (e) => handleProviderChange(e.target.value)); // Remove previous if any
            providerHiddenInput.addEventListener('change', (e) => handleProviderChange(e.target.value));
            console.log('Re-added provider change listener to hidden input:', providerHiddenInput.id);
        } else if (providerInput && providerInput.tagName === 'SELECT') {
            // Fallback for standard select (shouldn't happen with custom dropdown)
            providerInput.removeEventListener('change', (e) => handleProviderChange(e.target.value));
            providerInput.addEventListener('change', (e) => handleProviderChange(e.target.value));
            console.log('Added change listener to standard provider select');
        } else {
            console.warn('Could not find provider input (hidden or standard select) to attach change listener.');
        }
    }

    /**
     * Constants - model providers (populated dynamically based on availability)
     */
    let MODEL_PROVIDERS = [];

    /**
     * Load model options for the dropdown
     * @param {boolean} forceRefresh - Force refresh of model options
     * @returns {Promise} Promise that resolves with model options
     */
    function loadModelOptions(forceRefresh = false) {
        // Check if we already have cached models and haven't forced a refresh
        const cachedModels = getCachedData('deepResearch.availableModels');
        const cacheTimestamp = getCachedData('deepResearch.cacheTimestamp');

        if (!forceRefresh && cachedModels && Array.isArray(cachedModels) && cachedModels.length > 0 &&
            cacheTimestamp && (Date.now() - cacheTimestamp < 3600000)) { // 1 hour cache
            console.log('Using cached model options, count:', cachedModels.length);
            modelOptions = cachedModels;
            return Promise.resolve(cachedModels);
        }

        console.log('Loading model options from API' + (forceRefresh ? ' (forced refresh)' : ''));

        return fetchModelProviders(forceRefresh)
            .then(data => {
                // Don't overwrite our model options if the result is empty
                if (data && Array.isArray(data) && data.length > 0) {
                    modelOptions = data;
                    cacheData('deepResearch.availableModels', data);
                    console.log('Stored model options, count:', data.length);
                } else {
                    console.warn('API returned empty model data, keeping existing options');
                }
                return modelOptions;
            })
            .catch(error => {
                console.error('Error loading model options:', error);
                // Log but don't throw, so we can continue with default models if needed
                if (!modelOptions || modelOptions.length === 0) {
                    console.log('No fallback models - using empty array (dynamic fetching only)');
                    modelOptions = [];
                }
                return modelOptions;
            });
    }

    /**
     * Create a refresh button for a dropdown input
     * @param {string} inputId - The ID of the input to create a refresh button for
     * @param {Function} fetchFunc - The function to call when the button is clicked
     */
    function createRefreshButton(inputId, fetchFunc) {
        console.log('Creating refresh button for', inputId);
        // Check if the input exists
        const input = document.getElementById(inputId);
        if (!input) {
            console.warn(`Cannot create refresh button for non-existent input: ${inputId}`);
            return null;
        }

        // Find the parent container
        const container = input.closest('.form-group');
        if (!container) {
            console.warn(`Cannot find container for input: ${inputId}`);
            return null;
        }

        // Create a new button
        const refreshBtn = document.createElement('button');
        refreshBtn.type = 'button';
        refreshBtn.id = inputId + '-refresh';
        refreshBtn.className = 'custom-dropdown-refresh-btn';
        refreshBtn.setAttribute('aria-label', 'Refresh options');
        refreshBtn.style.display = 'flex';
        refreshBtn.style.alignItems = 'center';
        refreshBtn.style.justifyContent = 'center';
        refreshBtn.style.width = '38px';
        refreshBtn.style.height = '38px';
        refreshBtn.style.backgroundColor = 'var(--bg-tertiary, #2a2a3a)';
        refreshBtn.style.border = '1px solid var(--border-color, #343452)';
        refreshBtn.style.borderRadius = '6px';
        refreshBtn.style.cursor = 'pointer';
        refreshBtn.style.marginLeft = '8px';

        // Add icon to the button
        const icon = document.createElement('i');
        icon.className = 'fas fa-sync-alt';
        refreshBtn.appendChild(icon);

        // Add event listener to the button
        refreshBtn.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();

            console.log('Refresh button clicked for', inputId);
            icon.className = 'fas fa-spinner fa-spin';

            // Reset initialization flags
            if (inputId.includes('llm') || inputId.includes('model')) {
                window.modelDropdownsInitialized = false;
            } else if (inputId.includes('search') || inputId.includes('tool')) {
                window.searchEngineDropdownInitialized = false;
            }

            // Call the function directly as a parameter
            fetchFunc(true).then(() => {
                icon.className = 'fas fa-sync-alt';

                // Re-initialize appropriate dropdowns
                if (inputId.includes('llm') || inputId.includes('model')) {
                    initializeModelDropdowns();
                } else if (inputId.includes('search') || inputId.includes('tool')) {
                    initializeSearchEngineDropdowns();
                }

                showAlert(`Options refreshed`, 'success');
            }).catch(error => {
                console.error('Error refreshing options:', error);
                icon.className = 'fas fa-sync-alt';
                showAlert('Failed to refresh options', 'error');
            });
        });

        // Find the input wrapper or create one
        let inputWrapper = input.parentElement;
        if (inputWrapper.classList.contains('custom-dropdown-input')) {
            inputWrapper = inputWrapper.parentElement;
        }

        if (inputWrapper) {
            // Add the button after the input
            inputWrapper.style.display = 'flex';
            inputWrapper.style.alignItems = 'center';
            inputWrapper.style.gap = '8px';
            inputWrapper.appendChild(refreshBtn);
            console.log('Created new refresh button for:', inputId);
            return refreshBtn;
        }

        console.warn(`Could not find a suitable place to add refresh button for ${inputId}`);
        return null;
    }
})();
